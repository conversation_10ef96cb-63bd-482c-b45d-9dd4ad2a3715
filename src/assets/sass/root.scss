/* CODESTITCH STYLES, RESET, HEADER/NAVIGATION AND FOOTER */

/*-- -------------------------- -->
<---        Core Styles         -->
<--- -------------------------- -*/

/* CodeStitch Root and Helpers */
@media only screen and (min-width: 0rem) {
    // Website colors
    :root {
        --primary: #4f8bba;
        --primaryLight: #ffd675;
        --secondary: #346285;
        --secondaryLight: #346285;
        --headerColor: #1a1a1a;
        --bodyTextColor: #4e4b66;
        --bodyTextColorWhite: #fafbfc;

        /* 13px - 16px */
        --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
        /* 31px - 49px */
        --headerFontSize: clamp(1.9375rem, 3.9vw, 3.0625rem);
        --bodyFontSize: 1rem;

        /* 60px - 100px top and bottom */
        --sectionPadding: clamp(3.75rem, 7.82vw, 6.25rem) 1rem;
    }

    // Text styles - can be replaced using CodeStitch's "Content Flair" Stitches
    .cs-topper {
        display: block;
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        font-size: var(--topperFontSize);
        line-height: 1.2em;
        font-weight: 700;
        color: var(--primary);
        text-align: inherit;
    }

    .cs-title {
        position: relative;
        margin: 0 0 1rem 0;
        max-width: 43.75rem;
        font-size: var(--headerFontSize);
        line-height: 1.2em;
        font-weight: 900;
        color: var(--headerColor);
        text-align: inherit;
    }

    .cs-text {
        margin: 0;
        max-width: 40.625rem;
        width: 100%;
        font-size: var(--bodyFontSize);
        line-height: 1.5em;
        color: var(--bodyTextColor);
        text-align: inherit;
    }

    // Button - can be replaced using CodeStitch's "Button" Stitches
    .cs-button-solid {
        z-index: 1;
        position: relative;
        display: inline-block;
        background-color: var(--primary);
        width: auto;
        padding: 0 calc(30 / 16 * 1rem);
        text-decoration: none;
        text-transform: uppercase;
        font-size: calc(16 / 16 * 1rem);
        line-height: calc(50 / 16 * 1em);
        font-weight: bold;

        // Transition Properties
        color: #000;
        transition: color 0.3s;
        transition-delay: 0.1s;
        text-align: center;

        &:hover {
            color: #fff;
            &:before {
                width: 100%;
            }
        }

        &:before {
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            content: "";
            opacity: 1;
            display: block;
            background-color: #000;
            height: 100%;

            //Transition properties
            width: 0;
            transition: width 0.3s;
        }
    }

    .cs-hide-on-mobile {
        display: none;
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    .cs-hide-on-mobile {
        display: block;
    }

    .cs-hide-on-desktop {
        display: none;
    }
}

/* Fonts and general styles */
@media only screen and (min-width: 0rem) {
    body,
    html {
        margin: 0;
        overflow-x: hidden;
        padding: 0;
        font-family: "Maven Pro", Arial, sans-serif;
        font-size: 100%;
        color: var(--bodyTextColor);
    }

    *,
    *:before,
    *:after {
        margin: 0;
        box-sizing: border-box;
        padding: 0;
    }

    body {
        transition: background-color 0.3s;
    }

    .container {
        position: relative;
        margin: auto;
        width: 92%;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        margin: 0;
        color: var(--headerColor);
    }

    p,
    li,
    a {
        margin: 0;
        font-size: calc(16 / 16 * 1rem);
        line-height: 1.5em;
    }

    p,
    li {
        color: #353535;
    }

    a,
    button {
        &:hover {
            cursor: pointer;
        }
    }

    // Hidden Screen reader skip nav button
    .skip {
        z-index: -1111111;
        position: absolute;
        top: 0;
        left: 0;
        opacity: 0;
    }
    // Grab your fonts to locally host from https://google-webfonts-helper.herokuapp.com/fonts

    /* maven-pro-regular - latin */
    @font-face {
        font-display: swap;
        /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
        font-family: 'Maven Pro';
        font-style: normal;
        font-weight: 400;
        src: url('../fonts/maven-pro-v39-latin-regular.woff2') format('woff2');
        /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
    }

    /* maven-pro-700 - latin */
    @font-face {
        font-display: swap;
        /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
        font-family: 'Maven Pro';
        font-style: normal;
        font-weight: 700;
        src: url('../fonts/maven-pro-v39-latin-700.woff2') format('woff2');
        /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
    }

    /* maven-pro-900 - latin */
    @font-face {
        font-display: swap;
        /* Check https://developer.mozilla.org/en-US/docs/Web/CSS/@font-face/font-display for other options. */
        font-family: 'Maven Pro';
        font-style: normal;
        font-weight: 900;
        src: url('../fonts/maven-pro-v39-latin-900.woff2') format('woff2');
        /* Chrome 36+, Opera 23+, Firefox 39+, Safari 12+, iOS 10+ */
    }
}

/* Reset Margins */
@media only screen and (min-width: 1024px) {
    body,
    html {
        margin: 0;
        padding: 0;
    }
}

/* Scale full website with the viewport width */
@media only screen and (min-width: 3000px) {
    body,
    html {
        font-size: 0.55vw;
    }
}

/*-- -------------------------- -->
<---     Mobile Navigation      -->
<--- -------------------------- -*/

/* Mobile - 1023px */
@media only screen and (max-width: 1023px) {
    body {
        &.cs-open {
            overflow: hidden;
        }

        &.scroll {
            #cs-navigation {
                /* 53px, same height as the cs-top-container */
                transform: translateY(-3.3125rem);
            }
        }
    }

    #cs-navigation {
        width: 100%;
        /* prevents padding from affecting height and width */
        box-sizing: border-box;
        background-color: #fff;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
        position: fixed;
        z-index: 10000;
        transition: transform .3s;

        &:before {
            content: '';
            width: 100%;
            height: 0vh;
            background: rgba(0, 0, 0, .6);
            opacity: 0;
            display: block;
            position: absolute;
            top: 100%;
            right: 0;
            z-index: -1100;
            transition: height .5s, opacity .5s;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }

        &.cs-active {
            &:before {
                height: 150vh;
                opacity: 1;
            }

            .cs-ul-wrapper {
                opacity: 1;
                transform: scaleY(1);
                transition-delay: .15s;
            }

            .cs-li {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .cs-top-bar {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cs-top-container {
            width: 100%;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            padding: calc(16/16 * 1rem) calc(24/16 * 1rem);
            background-color: #f7f7f7;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: calc(16/16 * 1rem);
        }

        .cs-top-contact {
            width: auto;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            /* 16px - 24px */
            gap: clamp(1rem, 2vw, 1.5rem);
        }

        .cs-top-link {
            font-size: calc(14/16 * 1rem);
            line-height: 1.5em;
            text-decoration: none;
            margin: 0;
            color: var(--bodyTextColor);
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(8/16 * 1rem);
            position: relative;

            &:nth-of-type(2) {
                display: none;
            }
        }

        .cs-link-icon {
            width: calc(16/16 * 1rem);
            height: auto;
            display: block;
        }

        .cs-top-social {
            opacity: 1;
            display: flex;
            visibility: visible;
            justify-content: center;
            align-items: center;
            gap: calc(8/16 * 1rem);
            transition: opacity .3s, visibility .3s, height .3s;
        }

        .cs-social-link {
            text-decoration: none;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: calc(16/16 * 1rem);
        }

        .cs-social-icon {
            width: calc(20/16 * 1rem);
            height: auto;
            display: block;
        }

        .cs-container {
            width: 100%;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            padding: calc(20/16 * 1rem) calc(16/16 * 1rem);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: relative;
        }

        .cs-logo {
            width: auto;
            height: calc(40/16 * 1rem);
            margin: 0 auto 0 0;
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            order: 1;
            z-index: 10;

            img {
                width: auto;
                height: 100%;
                /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
                object-fit: contain;
            }
        }

        .cs-nav {
            order: 2;
        }

        .cs-toggle {
            width: calc(46/16 * 1rem);
            height: calc(46/16 * 1rem);
            margin: 0 0 0 auto;
            background-color: var(--primary);
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform .6s;

            &.cs-active {
                transform: rotate(180deg);
            }
        }

        .cs-active {
            .cs-line1 {
                top: 50%;
                transform: translate(-50%, -50%) rotate(225deg);
            }

            .cs-line2 {
                top: 50%;
                transform: translate(-50%, -50%) translateY(0) rotate(-225deg);
                transform-origin: center;
            }

            .cs-line3 {
                opacity: 0;
                bottom: 100%;
            }
        }

        .cs-box {
            /* 24px - 28px */
            width: clamp(1.5rem, 2vw, 1.75rem);
            height: calc(12/16 * 1rem);
            position: relative;
        }

        .cs-line {
            width: 100%;
            height: 2px;
            background-color: #FAFBFC;
            border-radius: 2px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        .cs-line1 {
            top: 0;
            transition: transform .5s, top .3S, left .3S;
            animation-duration: .7s;
            animation-timing-function: ease;
            animation-direction: normal;
            animation-fill-mode: forwards;
            transform-origin: center;
        }

        .cs-line2 {
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            transition: top .3s, left .3s, transform .5s;
            animation-duration: .7s;
            animation-timing-function: ease;
            animation-direction: normal;
            animation-fill-mode: forwards;
        }

        .cs-line3 {
            bottom: 0;
            transition: bottom .3s, opacity .3s;
        }

        .cs-ul-wrapper {
            width: 100%;
            height: auto;
            padding-bottom: calc(48/16 * 1rem);
            background-color: #fff;
            box-shadow: inset rgba(0, 0, 0, 0.2) 0px 8px 24px;
            opacity: 0;
            position: absolute;
            top: 100%;
            left: 0;
            z-index: -1;
            overflow: hidden;
            transform: scaleY(0);
            transition: transform .4s, opacity .3s;
            transform-origin: top;
        }

        .cs-ul {
            width: 100%;
            height: auto;
            max-height: 65vh;
            margin: 0;
            padding: calc(48/16 * 1rem) 0 0 0;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            gap: calc(20/16 * 1rem);
            overflow: scroll;
        }

        .cs-li {
            text-align: center;
            list-style: none;
            width: 100%;
            margin-right: 0;
            opacity: 0;
            /* transition from these values */
            transform: translateY(-4.375rem);
            transition: transform .6s, opacity .9s;

            &:nth-of-type(1) {
                transition-delay: .05s;
            }

            &:nth-of-type(2) {
                transition-delay: .1s;
            }

            &:nth-of-type(3) {
                transition-delay: .15s;
            }

            &:nth-of-type(4) {
                transition-delay: .2s;
            }

            &:nth-of-type(5) {
                transition-delay: .25s;
            }

            &:nth-of-type(6) {
                transition-delay: .3s;
            }

            &:nth-of-type(7) {
                transition-delay: .35s;
            }

            &:nth-of-type(8) {
                transition-delay: .4s;
            }

            &:nth-of-type(9) {
                transition-delay: .45s;
            }

            &:nth-of-type(10) {
                transition-delay: .5s;
            }

            &:nth-of-type(11) {
                transition-delay: .55s;
            }

            &:nth-of-type(12) {
                transition-delay: .6s;
            }

            &:nth-of-type(13) {
                transition-delay: .65s;
            }
        }

        .cs-li-link {
            /* 16px - 24px */
            font-size: clamp(1rem, 2.5vw, 1.5rem);
            line-height: 1.2em;
            text-decoration: none;
            margin: 0;
            color: var(--headerColor);
            display: inline-block;
            position: relative;

            &.cs-active {
                color: var(--primary);
            }

            &:hover {
                color: var(--primary);
            }
        }

        .cs-button-solid {
            display: none;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #cs-navigation {
        .cs-top-link {
            &:nth-of-type(2) {
                display: flex;
            }
        }
    }
}

/* Dark Mode */
@media only screen and (max-width: 1023px) {
    body.dark-mode {
        #cs-navigation {
            background-color: var(--dark);

            .cs-logo {
                /* makes it white */
                filter: grayscale(1) brightness(1000%);
            }

            .cs-top-container {
                background-color: rgba(0, 0, 0, .2);
            }

            .cs-top-link,
            .cs-desc {
                color: var(--bodyTextColorWhite);
                opacity: .8;
            }

            .cs-link-icon {
                filter: grayscale(1) brightness(1000%);
            }

            .cs-ul-wrapper {
                background-color: var(--medium);
            }

            .cs-li-link,
            .cs-header {
                color: var(--bodyTextColorWhite);
            }
        }
    }
}

/*-- -------------------------- -->
<---     Navigation Dropdown    -->
<--- -------------------------- -*/
/* Mobile - 1023px */
@media only screen and (max-width: 1023px) {
    #cs-navigation {
        .cs-li {
            text-align: center;
            width: 100%;
            display: block;
        }

        .cs-dropdown {
            color: var(--bodyTextColorWhite);
            position: relative;

            &.cs-active {
                .cs-drop-ul {
                    height: auto;
                    margin: calc(12/16 * 1rem) 0 0 0;
                    padding: calc(12/16 * 1rem) 0;
                    opacity: 1;
                    visibility: visible;
                }

                .cs-drop-link {
                    opacity: 1;
                }
            }

            .cs-li-link {
                position: relative;
                transition: opacity .3s;
            }
        }

        .cs-drop-icon {
            width: calc(15/16 * 1rem);
            height: auto;
            position: absolute;
            top: 50%;
            right: calc(-20/16 * 1rem);
            transform: translateY(-50%);
        }

        .cs-drop-ul {
            width: 100%;
            height: 0;
            margin: 0;
            padding: 0;
            background-color: var(--primary);
            opacity: 0;
            display: flex;
            visibility: hidden;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            gap: calc(12/16 * 1rem);
            overflow: hidden;
            transition: padding .3s, margin .3s, height .3s, opacity .3s, visibility .3s;
        }

        .cs-drop-li {
            list-style: none;
        }

        .cs-li-link {
            &.cs-drop-link {
                /* 14px - 16px */
                font-size: clamp(.875rem, 2vw, 1.25rem);
                color: #fff;
            }
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #cs-navigation {
        .cs-dropdown {
            position: relative;

            &:hover {
                cursor: pointer;

                .cs-drop-ul {
                    opacity: 1;
                    visibility: visible;
                    transform: scaleY(1);
                }

                .cs-drop-li {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }

        .cs-drop-icon {
            width: calc(12/16 * 1rem);
            height: auto;
            margin-left: calc(4/16 * 1rem);
            display: inline-block;
        }

        .cs-drop-ul {
            min-width: calc(200/16 * 1rem);
            margin: 0;
            padding: 0;
            background-color: #fff;
            box-shadow: rgba(149, 157, 165, 0.2) 0px 10px 16px;
            opacity: 0;
            border-bottom: 5px solid var(--primary);
            visibility: hidden;
            /* if you have 8 or more links in your dropdown nav, uncomment the columns property to make the list into 2 even columns. Change it to 3 or 4 if you need extra columns. Then remove the transition delays on the cs-drop-li so they don't have weird scattered animations */
            // columns: 2;
            position: absolute;
            top: 100%;
            z-index: -100;
            overflow: hidden;
            transform: scaleY(0);
            transition: transform .3s, visibility .3s, opacity .3s;
            transform-origin: top;
        }

        .cs-drop-li {
            font-size: calc(16/16 * 1rem);
            text-decoration: none;
            list-style: none;
            width: 100%;
            height: auto;
            opacity: 0;
            display: block;
            transform: translateY(calc(-10/16 * 1rem));
            transition: opacity .6s, transform .6s;

            &:nth-of-type(1) {
                transition-delay: .05s;
            }

            &:nth-of-type(2) {
                transition-delay: .1s;
            }

            &:nth-of-type(3) {
                transition-delay: .15s;
            }

            &:nth-of-type(4) {
                transition-delay: .2s;
            }

            &:nth-of-type(5) {
                transition-delay: .25s;
            }

            &:nth-of-type(6) {
                transition-delay: .3s;
            }

            &:nth-of-type(7) {
                transition-delay: .35s;
            }

            &:nth-of-type(8) {
                transition-delay: .4s;
            }

            &:nth-of-type(9) {
                transition-delay: .45s;
            }

            &:nth-of-type(10) {
                transition-delay: .5s;
            }

            &:nth-of-type(11) {
                transition-delay: .55s;
            }

            &:nth-of-type(12) {
                transition-delay: .6s;
            }

            &:nth-of-type(13) {
                transition-delay: .65s;
            }
        }

        .cs-li-link {
            &.cs-drop-link {
                font-size: calc(16/16 * 1rem);
                line-height: 1.5em;
                text-decoration: none;
                white-space: nowrap;
                width: 100%;
                /* prevents padding from affecting height and width */
                box-sizing: border-box;
                padding: calc(12/16 * 1rem);
                color: var(--bodyTextColor);
                display: block;
                transition: color .3s, background-color .3s;

                &:hover {
                    color: var(--primary);
                    background-color: #f7f7f7;
                }

                &:before {
                    display: none;
                }
            }
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #cs-navigation {
            .cs-drop-ul {
                background-color: var(--dark);
            }

            .cs-drop-icon {
                filter: grayscale(1) brightness(1000%);
            }

            .cs-li-link {
                &.cs-drop-link {
                    &:hover {
                        color: var(--bodyTextColorWhite);
                        background-color: rgba(255, 255, 255, .2);
                    }

                    &:before {
                        display: none;
                    }
                }
            }
        }
    }
}

/*-- -------------------------- -->
<---     Desktop Navigation     -->
<--- -------------------------- -*/
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    body.scroll {
        #cs-navigation {
            /* 53px, the ssme height as the cs-top-container */
            transform: translateY(-3.3125rem);
        }
    }

    #cs-navigation {
        width: 100%;
        /* prevents padding from affecting height and width */
        box-sizing: border-box;
        padding: 0;
        background-color: #fff;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
        position: fixed;
        z-index: 10000;
        transition: transform .3s;

        .cs-top-bar {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .cs-top-container {
            width: 100%;
            max-width: calc(1280/16 * 1rem);
            /* prevents padding from affecting height and width */
            box-sizing: border-box;
            padding: calc(16/16 * 1rem);
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: calc(50/16 * 1rem);
            position: relative;
            z-index: 1;

            &:before {
                /* grey background */
                content: '';
                width: 100vw;
                height: 100%;
                background: #f7f7f7;
                opacity: 1;
                display: block;
                position: absolute;
                top: 0;
                left: 50%;
                z-index: -1;
                transform: translateX(-50%);
            }
        }

        .cs-top-contact {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(24/16 * 1rem);
        }

        .cs-top-link {
            font-size: calc(14/16 * 1rem);
            line-height: 1.5em;
            text-decoration: none;
            margin: 0;
            color: var(--bodyTextColor);
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(8/16 * 1rem);
            position: relative;

            &:hover {
                text-decoration: underline;
            }
        }

        .cs-link-icon {
            width: calc(16/16 * 1rem);
            height: auto;
            display: block;
        }

        .cs-top-social {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: calc(16/16 * 1rem);
        }

        .cs-social-link {
            text-decoration: none;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: transform .3s;

            &:hover {
                transform: scale(1.1);
            }
        }

        .cs-social-icon {
            width: calc(20/16 * 1rem);
            height: auto;
            display: block;
        }

        .cs-container {
            width: 100%;
            max-width: calc(1280/16 * 1rem);
            margin: auto;
            /* prevents padding from affectin gheight */
            box-sizing: border-box;
            padding: 0 calc(16/16 * 1rem);
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: calc(24/16 * 1rem);
            position: relative;
        }

        .cs-toggle {
            display: none;
        }

        .cs-logo {
            /* 40px - 44px */
            height: clamp(2.5rem, 4vw, 2.75rem);
            /* margin-right auto pushes everything away from it to the right */
            margin: 0 auto 0 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;

            img {
                width: auto;
                height: 100%;
                /* ensures the image never overflows the container. It stays contained within it's width and height and expands to fill it then stops once it reaches an edge */
                object-fit: contain;
            }
        }

        .cs-ul {
            width: 100%;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            gap: calc(48/16 * 1rem);
        }

        .cs-li {
            list-style: none;
            padding: calc(31/16 * 1rem) 0;
            /* prevent flexbox from squishing it */
            flex: none;
        }

        .cs-li-link {
            /* 14px - 16px */
            font-size: clamp(.875rem, 1.3vw, 1rem);
            line-height: 1.5em;
            text-decoration: none;
            margin: 0;
            color: var(--headerColor);
            display: block;
            position: relative;
            transition: color .3s;

            &:hover {
                color: var(--primary);
            }

            &.cs-active {
                font-weight: 700;
                color: var(--headerColor);
            }
        }

        .cs-button-solid {
            font-size: calc(16/16 * 1rem);
            font-weight: 700;
            /* 46px - 56px */
            line-height: clamp(2.875em, 5.5vw, 3.5em);
            text-align: center;
            text-decoration: none;
            margin: 0;
            /* prevents padding from adding to the width */
            box-sizing: border-box;
            padding: 0 calc(32/16 * 1rem);
            color: #fff;
            background-color: var(--primary);
            display: inline-block;
            position: relative;
            z-index: 1;
            overflow: hidden;
            transition: color .3s;

            &:before {
                content: '';
                width: 0%;
                height: 100%;
                background: #1a1a1a;
                opacity: 1;
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width .3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }
        }

        .cs-nav-button {
            line-height: calc(46/16 * 1rem);
            margin-left: calc(24/16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 64rem) {
    body.dark-mode {
        #cs-navigation {
            background-color: var(--dark);

            .cs-top-container {
                &:before {
                    background-color: rgba(0, 0, 0, .2);
                }
            }

            .cs-li-link,
            .cs-top-link {
                color: var(--bodyTextColorWhite);
            }

            .cs-li-link,
            .cs-top-link {
                &:hover {
                    color: var(--secondary);
                }
            }

            .cs-top-link {
                opacity: .8;
            }

            .cs-logo,
            .cs-link-icon {
                /* turns it white */
                filter: grayscale(1) brightness(1000%);
            }
        }
    }
}

/*-- -------------------------- -->
<---   Interior Page Header     -->
<--- -------------------------- -*/

/* Mobile */
@media only screen and (min-width: 0rem) {
    #int-hero {
        z-index: 1;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        min-height: 30vh;
        padding-top: calc(100 / 16 * 1rem);

        picture {
            z-index: -2;
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        h1 {
            position: relative;
            margin: 0 auto;
            margin-top: calc(70 / 16 * 1rem);
            margin-bottom: calc(30 / 16 * 1rem);
            max-width: calc(800 / 16 * 1rem);
            width: 96%;
            font-size: calc(64 / 30 * 1rem);
            color: #fff;
            text-align: center;
        }

        p {
            display: block;
            margin: auto;
            margin-bottom: calc(30 / 16 * 1rem);
            max-width: calc(400 / 16 * 1rem);
            width: 96%;
            color: #fff;
            text-align: center;
        }

        &:before {
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            content: "";
            opacity: 0.7;
            display: block;
            background: #000;
            height: 100%;
            width: 100%;
        }
    }
}

/* Tablet */
@media only screen and (min-width: 48rem) {
    #int-hero {
        font-size: 100%;

        h1 {
            font-size: calc(64 / 16 * 1rem);
        }
    }
}

/* Small Desktop */
@media only screen and (min-width: 64rem) {
    #int-hero {
        background-attachment: fixed;
        min-height: calc(300 / 16 * 1rem);
        height: auto;
        padding-top: calc(180 / 16 * 1rem);
        font-size: inherit;
        padding-block-end: calc(100 / 16 * 1rem);
    }
}

/*-- -------------------------- -->
<---          Footer            -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #cs-footer-1763 {
        padding: var(--sectionPadding);
        padding-bottom: clamp(1.875rem, 5vw, 3.125rem);
        /* change this to match the color of the section above the footer */
        background-color: #f7f7f7;
        overflow: hidden;
        /* 30px - 50px */
        position: relative;
        z-index: 1;

        .cs-card-group {
            /* reset on tablet */
            max-width: calc(550/16 * 1rem);
            margin: auto;
            /* 60px - 100px*/
            padding: 0 0 clamp(3.75rem, 7vw, 6.25rem) 0;
            display: grid;
            /* 20px - 58px */
            gap: clamp(1.25rem, 4vw, 3.625rem);
        }

        .cs-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            /* 16px - 24px */
            gap: clamp(1rem, 2vw, 1.5rem);
        }

        .cs-picture {
            padding: calc(24/16 * 1rem);
            background-color: var(--primary);
            display: block;
        }

        .cs-icon {
            width: calc(32/16 * 1rem);
            height: auto;
            display: block;
        }

        .cs-h3 {
            /* 20px - 25px */
            font-size: clamp(1.25rem, 2vw, 1.5625rem);
            font-weight: 700;
            line-height: 1.2em;
            margin: 0;
            color: var(--headerColor);
        }

        .cs-card-info {
            font-size: calc(16/16 * 1rem);
            text-decoration: none;
            line-height: 1.5em;
            color: var(--bodyTextColor);
            display: block;
        }

        .cs-container {
            width: 100%;
            /* reset on tablet */
            max-width: calc(550/16 * 1rem);
            margin: auto;
            display: grid;
            grid-template-columns: 1fr;
            /* 32px - 48px */
            gap: clamp(2rem, 7vw, 3rem);
        }

        .cs-logo-group {
            /* takes up all the space, lets the other ul's wrap below it */
            width: 100%;
            position: relative;
        }

        .cs-logo {
            width: calc(210/16 * 1rem);
            height: auto;
            margin: 0 0 calc(16/16 * 1rem) 0;
            display: block;
        }

        .cs-text {
            max-width: calc(704/16 * 1rem);
            /* 24px - 40px */
            margin: 0 0 clamp(1.5rem, 4vw, 2.5rem);
            opacity: 0.8;
        }

        .cs-logo-img {
            width: 100%;
            height: auto;
        }

        .cs-contact {
            display: grid;
            gap: calc(32/16 * 1rem);
        }

        .cs-topper {
            font-size: calc(16/16 * 1rem);
            margin-bottom: calc(4/16 * 1rem);
        }

        .cs-contact-link {
            font-size: calc(20/16 * 1rem);
            font-weight: 700;
            line-height: 1.2em;
            text-decoration: none;
            color: var(--headerColor);
        }

        .cs-header {
            /* 20px - 25px */
            font-size: clamp(1.25rem, 3vw, 1.5625rem);
            font-weight: 700;
            line-height: 1.5em;
            /* 16px - 24px */
            margin: 0 0 clamp(1rem, 4vw, 1.5rem) 0;
            color: var(--headerColor);
            display: block;
            position: relative;
        }

        .cs-ul {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: calc(12/16 * 1rem);
        }

        .cs-li {
            list-style: none;
        }

        .cs-link {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            text-align: left;
            text-decoration: none;
            color: var(--bodyTextColor);
            display: inline-flex;
            align-items: flex-start;
            gap: calc(8/16 * 1rem);
            transition: color 0.3s;

            &:hover {
                color: var(--primaryLight);
            }
        }

        .cs-bottom {
            width: 100%;
            max-width: calc(1280/16 * 1rem);
            /* 60px - 80px */
            margin: clamp(3.75rem, 7vw, 5rem) auto 0;
            padding-top: calc(24/16 * 1rem);
            border-top: 1px solid var(--border-border-10, #e8e8e8);
            display: flex;
            flex-wrap: wrap;
            gap: calc(12/16 * 1rem);
        }

        .cs-credit,
        .cs-bottom-link,
        .cs-credit-link {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            text-decoration: none;
            color: var(--bodyTextColor);
        }

        .cs-credit {
            width: 100%;
        }

        .cs-credit-link {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            text-decoration: none;
            width: auto;
            margin: 0;
            display: inline-block;
            position: relative;

            &:hover {
                color: var(--primary);
            }
        }

        .cs-bottom-links {
            width: 100%;
            display: flex;
        }

        .cs-bottom-link {
            display: flex;
            align-items: center;

            &:hover {
                color: var(--primary);
            }

            &:last-of-type {
                &:before {
                    /* separator */
                    content: "";
                    width: 1px;
                    height: calc(14/16 * 1rem);
                    margin: 0 calc(12/16 * 1rem);
                    background: currentColor;
                    opacity: 1;
                    display: block;
                }
            }
        }

        .cs-graphic {
            width: 100%;
            min-width: calc(1920/16 * 1rem);
            height: auto;
            object-fit: cover;
            position: absolute;
            top: calc(144/16 * 1rem);
            left: 50%;
            z-index: -1;
            transform: translateX(-50%);
        }

        .cs-light {
            display: block;
        }

        .cs-dark {
            display: none;
        }
    }
}

/* Tablet - 600px */
@media only screen and (min-width: 37.5rem) {
    #cs-footer-1763 {
        .cs-card-group {
            max-width: calc(1280/16 * 1rem);
        }

        .cs-container {
            padding: var(--sectionPadding);
            padding-left: 0;
            padding-right: 0;
            padding-bottom: 0;
            max-width: calc(1280/16 * 1rem);
            grid-template-columns: repeat(12, 1fr);
        }

        .cs-logo-group {
            grid-column: span 12;
        }

        .cs-ul-wrapper {
            grid-column: span 4;
        }

        .cs-contact {
            grid-template-columns: repeat(12, 1fr);
            grid-column: span 12;
        }

        .cs-table {
            grid-column: span 12;
        }

        .cs-bottom {
            justify-content: space-between;
            flex-wrap: nowrap;
        }

        .cs-credit {
            text-align: left;
        }

        .cs-bottom-links {
            justify-content: flex-end;
        }
    }
}

/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #cs-footer-1763 {
        .cs-card-group {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
        }

        .cs-card {
            grid-column: span 4;
        }

        .cs-logo-group {
            grid-column: span 4;
        }

        .cs-ul-wrapper {
            grid-column: span 2;
        }

        .cs-contact {
            display: flex;
            flex-direction: column;
            grid-column: span 4;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #cs-footer-1763 {
            background-color: rgba(0, 0, 0, .2);

            .cs-logo {
                /* turns logo white */
                filter: grayscale(1) brightness(1000%);
            }

            .cs-h3,
            .cs-card-info,
            .cs-contact-link,
            .cs-header,
            .cs-link,
            .cs-credit,
            .cs-credit-link,
            .cs-bottom-link {
                color: var(--bodyTextColorWhite);
            }

            .cs-card-info,
            .cs-link,
            .cs-credit,
            .cs-credit-link,
            .cs-bottom-link {
                opacity: 0.8;
            }

            .cs-bottom {
                border-color: rgba((255), 255, 255, .1);
            }

            .cs-light {
                display: none;
            }

            .cs-dark {
                opacity: .5;
                display: block;
            }
        }
    }
}