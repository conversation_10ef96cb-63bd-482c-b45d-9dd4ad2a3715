/*-- -------------------------- -->
<---          Services          -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #services-1082 {
        padding: var(--sectionPadding);
        background-color: #f7f7f7;

        .cs-container {
            width: 100%;
            /* changes to 1440px at large desktop */
            max-width: calc(824 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 48px - 64px */
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
        }

        .cs-title {
            max-width: 20ch;
        }

        .cs-card-group {
            width: 100%;
            margin: 0;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            align-items: stretch;
            /* 16px - 20px */
            gap: clamp(1rem, 2vw, 1.25rem);
        }

        .cs-item {
            text-align: left;
            list-style: none;
            width: 100%;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            grid-column: span 12;
            position: relative;
            z-index: 1;

            &:hover {
                .cs-picture {
                    &:before {
                        opacity: 0.8;
                    }

                    img {
                        transform: scale(1.2);
                    }
                }

                .cs-h3 {
                    background-color: var(--primary);
                }
            }
        }

        .cs-picture {
            width: 100%;
            /* 180px - 240px */
            height: clamp(11.25rem, 24vw, 15rem);
            /* clips the image from overflowing parent on hover */
            overflow: hidden;
            display: block;
            position: relative;

            &:before {
                /* black hover overlay */
                content: "";
                width: 100%;
                height: 100%;
                background: #000;
                opacity: 0;
                position: absolute;
                display: block;
                top: 0;
                left: 0;
                transition: opacity 0.3s;
            }

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                transition: transform 0.65s;
            }
        }

        .cs-h3 {
            /* 20px - 25px */
            font-size: clamp(1.25rem, 2.5vw, 1.5625rem);
            line-height: 1.5em;
            font-weight: 700;
            text-align: inherit;
            width: 100%;
            margin: 0;
            /* 12px - 16px top & Bottom */
            /* 16px - 24px left & right */
            padding: clamp(0.75rem, 1.6vw, 1rem) clamp(1rem, 2.5vw, 1.5rem);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            background-color: #1a1a1a;
            color: var(--bodyTextColorWhite);
            display: flex;
            justify-content: flex-start;
            align-items: center;
            /* 8px - 16px */
            gap: clamp(0.5rem, 1vw, 1rem);
            transition: background-color 0.3s;
        }

        .cs-item-text {
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.5em;
            text-align: inherit;
            width: 100%;
            margin: 0 0 calc(20 / 16 * 1rem);
            /* 16px - 24px */
            padding: clamp(1rem, 2.5vw, 1.25rem);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            padding-bottom: 0;
            color: var(--bodyTextColor);
        }

        .cs-link {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.2em;
            text-align: inherit;
            text-decoration: none;
            font-weight: 700;
            /* 16px - 24px */
            margin: 0 0 clamp(1rem, 2.5vw, 1.5rem) clamp(1rem, 2.5vw, 1.5rem);
            color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: flex-start;

            &:hover {
                .cs-arrow {
                    transform: translateX(0.25rem);
                }
            }
        }

        .cs-arrow {
            width: calc(20 / 16 * 1rem);
            height: auto;
            display: block;
            transition: transform 0.3s;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #services-1082 {
        .cs-item {
            grid-column: span 6;
        }
    }
}

/* Large Desktop - 1300px */
@media only screen and (min-width: 81.25rem) {
    #services-1082 {
        .cs-container {
            max-width: calc(1440 / 16 * 1rem);
        }

        .cs-item {
            grid-column: span 3;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #services-1082 {
            background-color: rgba(0, 0, 0, 0.6);

            .cs-title,
            .cs-text,
            .cs-item-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-text,
            .cs-item-text {
                opacity: 0.8;
            }

            .cs-h3 {
                background-color: rgba(0, 0, 0, 0.9);
            }

            .cs-item {
                background-color: rgba(0, 0, 0, 0.4);
            }
        }
    }
}

/*-- -------------------------- -->
<---           Steps            -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #steps-1376 {
        padding: var(--sectionPadding);
        background-color: #fff;
        /* clips the waves and lines from cause overflow issues */
        overflow: hidden;

        .cs-container {
            max-width: calc(1440 / 16 * 1rem);
            width: 100%;
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 48px - 64px */
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
            /* set text align to left if content needs to be left aligned */
            text-align: center;
        }

        .cs-topper {
            color: #767676;
        }

        .cs-title {
            max-width: 25ch;
            margin: 0;
        }

        .cs-group1,
        .cs-group2 {
            /* the contents of this div are no longer its children. This div acts as if it doesn't exist and their content is now children of the next level parent, in this case, the cs-card-group. That way they can all be arranged together as children under 1 parent div instead of children in two different parent divs */
            display: contents;
        }

        .cs-card-group {
            width: 100%;
            margin: 0 auto;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            /* 16px - 20px */
            gap: clamp(1rem, 2.3vw, 1.25rem);
            position: relative;
        }

        .cs-item {
            list-style: none;
            width: 100%;
            max-width: calc(240 / 16 * 1rem);
            height: calc(240 / 16 * 1rem);
            margin: 0;
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            padding: calc(24 / 16 * 1rem);
            /* clips the :before element from being clickable when it's outside the item and triggering the hover animation */
            overflow: hidden;
            border-radius: 50%;
            border: 1px solid #e8e8e8;
            background-color: #F4F4F4;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;
            transition: background-color 0.3s, border-color 0.3s;
            text-align: center;

            &:before {
                content: '';
                width: calc(148 / 16 * 1rem);
                height: calc(148 / 16 * 1rem);
                background: #fff;
                border-radius: 50%;
                opacity: 0;
                position: absolute;
                display: block;
                bottom: 0;
                left: calc(-116 / 16 * 1rem);
                transform: translateY(6.25rem);
                transition: left .3s, transform .3s, opacity .3s;
            }

            &:hover {
                background-color: var(--primary);
                border-color: var(--primary);

                &:before {
                    opacity: .2;
                    left: calc(-16 / 16 * 1rem);
                    transform: translateY(0);
                }

                .cs-icon {
                    filter: grayscale(1) brightness(1000%);
                }

                .cs-h3,
                .cs-item-text {
                    color: var(--bodyTextColorWhite);
                }

                .cs-text {
                    opacity: .8;
                }
            }
        }

        .cs-picture {
            width: calc(100 / 16 * 1rem);
            height: auto;
            margin: 0 0 calc(8 / 16 * 1rem) 0;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            /* prevents flexbox from squishing it */
            flex: none;
        }

        .cs-icon {
            width: calc(32 / 16 * 1rem);
            height: auto;
        }

        .cs-h3 {
            font-size: calc(20 / 16 * 1rem);
            line-height: 1.2em;
            font-weight: bold;
            margin: 0 0 calc(12 / 16 * 1rem) 0;
            color: var(--headerColor);
            transition: color 0.3s;
            text-align: inherit;
            transition: color .3s;
        }

        .cs-item-text {
            font-size: calc(14 / 16 * 1rem);
            line-height: 1.5em;
            max-width: calc(450 / 16 * 1rem);
            margin: 0;
            padding: 0;
            color: var(--bodyTextColor);
            transition: color .3s, opacity .3s;
        }

        .cs-waves {
            display: none;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #steps-1376 {
        .cs-card-group {
            justify-content: center;
            align-items: center;
            gap: calc(40 / 16 * 1rem);
        }

        .cs-group1 {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            gap: calc(48 / 16 * 1rem);
        }

        .cs-group2 {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-evenly;
            gap: calc(48 / 16 * 1rem);
        }

        .cs-item {
            /* prevents flexbox from squishing it */
            flex: none;
        }

        .cs-waves {
            width: calc(71 / 16 * 1rem);
            height: auto;
            display: block;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);

            img {
                width: 100%;
                height: auto;
            }
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #steps-1376 {
        .cs-card-group {
            flex-direction: column;
        }

        .cs-group1 {
            width: 100%;
            /* -8px to -30px, we put the clamp in a calc function to turn it into a negative number and pull down to cause the overlapping effect */
            margin-bottom: calc(clamp(.5rem, 2vw, 1.875rem)*-1);
            flex-direction: row;
        }

        .cs-group2 {
            width: 100%;
            /* -8px to -30px */
            margin-top: calc(clamp(.5rem, 2vw, 1.875rem)*-1);
            flex-direction: row;
        }

        .cs-waves {
            width: 110vw;
            max-width: calc(1920 / 16 * 1rem);
            height: auto;

            &:before {
                /* left continuation line */
                content: '';
                width: 50vw;
                height: 4px;
                background: #e8e8e8;
                opacity: 1;
                position: absolute;
                display: block;
                top: calc(84 / 16 * 1rem);
                right: 100%;
            }

            &:after {
                /* Right continuation line */
                content: '';
                width: 50vw;
                height: 4px;
                background: #e8e8e8;
                opacity: 1;
                position: absolute;
                display: block;
                top: calc(84 / 16 * 1rem);
                left: 100%;
            }
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #steps-1376 {
            background-color: rgba(0, 0, 0, .2);

            .cs-item {
                background-color: var(--medium);
                border-color: rgba(255, 255, 255, 0.2);

                &:hover {
                    background-color: var(--primary);
                    border-color: var(--primary);
                }
            }

            .cs-topper {
                color: var(--primary);
            }

            .cs-title,
            .cs-text,
            .cs-h3,
            .cs-item-text,
            .cs-link {
                color: var(--bodyTextColorWhite);
            }

            .cs-text,
            .cs-item-text {
                opacity: 0.8;
            }

            .cs-waves {
                opacity: .1;
            }
        }
    }
}

/*-- -------------------------- -->
<---          Pricing           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #pricing-1319 {
        padding: var(--sectionPadding);
        /* clips the cs-floater and prevents it from causing overflow issues */
        overflow: hidden;
        background-color: #f7f7f7;
        position: relative;
        z-index: 1;

        .cs-container {
            width: 100%;
            /* changes to 1440px at tablet */
            max-width: calc(584 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 48px - 64px */
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: center;
            width: 100%;
            max-width: calc(487 / 16 * 1rem);
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
        }

        .cs-title {
            max-width: 20ch;
        }

        .cs-text {
            margin-bottom: calc(16 / 16 * 1rem);

            &:last-of-type {
                margin-bottom: calc(32 / 16 * 1rem);
            }
        }

        .cs-button-solid {
            font-size: calc(16 / 16 * 1rem);
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            margin: 0;
            color: #1a1a1a;
            min-width: calc(150 / 16 * 1rem);
            padding: 0 calc(24 / 16 * 1rem);
            background-color: var(--primary);
            display: inline-block;
            position: relative;
            z-index: 1;
            /* prevents padding from adding to the width */
            box-sizing: border-box;
            transition: color 0.3s;

            &:before {
                content: "";
                position: absolute;
                height: 100%;
                width: 0%;
                background: #000;
                opacity: 1;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width 0.3s;
            }

            &:hover {
                color: #fff;

                &:before {
                    width: 100%;
                }
            }
        }

        .cs-card-group {
            width: 100%;
            max-width: calc(852 / 16 * 1rem);
            padding: 0;
            margin: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            /* 16px - 20px */
            gap: clamp(1rem, 2.3vw, 1.25rem);
        }

        .cs-item {
            text-align: center;
            list-style: none;
            width: 100%;
            margin: 0;
            /* 48px - 64px top & bootm */
            /* 16px - 24px left & right */
            padding: clamp(3rem, 4.4vw, 4rem) clamp(1rem, 2.3vw, 1.5rem);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;

            &.cs-popular {
                background-color: #1a1a1a;

                .cs-ul {
                    &:before {
                        opacity: 0.5;
                    }
                }

                .cs-package,
                .cs-desc,
                .cs-price,
                .cs-duration,
                .cs-li {
                    color: var(--bodyTextColorWhite);
                }

                .cs-desc,
                .cs-duration,
                .cs-li {
                    opacity: 0.8;
                }

                .cs-button-transparent {
                    border: none;
                    background-color: var(--primary);
                    color: #1a1a1a;

                    &:before {
                        background-color: #fff;
                    }
                }
            }
        }

        .cs-package {
            /* 13px - 16px */
            font-size: clamp(0.8125rem, 1.5vw, 1rem);
            line-height: 1.2em;
            text-align: inherit;
            text-transform: uppercase;
            font-weight: 700;
            /* 4px - 8px */
            margin: 0 0 clamp(0.25rem, 1vw, 0.5rem) 0;
        }

        .cs-desc {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            text-align: inherit;
            margin: 0 0 calc(16 / 16 * 1rem) 0;
            color: var(--bodyTextColor);
            display: block;
        }

        .cs-price {
            /* 31px - 49px */
            font-size: clamp(1.9375rem, 4vw, 3.0625rem);
            line-height: 1.2em;
            font-weight: 700;
            text-align: inherit;
            margin: 0;
            padding: 0;
            color: var(--headerColor);
            display: flex;
            justify-content: center;
            align-items: flex-end;
        }

        .cs-duration {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            font-weight: 400;
            text-align: inherit;
            margin: 0;
            /* 4px - 8px */
            padding: clamp(0.25rem, 1vw, 0.5rem) 0;
            color: var(--bodyTextColor);
            display: block;
        }

        .cs-ul {
            width: 100%;
            /* 16px - 24px */
            margin: clamp(1rem, 2vw, 1.25rem) 0 auto 0;
            padding: clamp(1rem, 2vw, 1.25rem) 0 0 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 8px - 12px */
            gap: clamp(0.5rem, 1.5vw, 0.75rem);
            position: relative;

            &:before {
                content: "";
                width: 100%;
                max-width: calc(250 / 16 * 1rem);
                height: 1px;
                background: linear-gradient(90deg,
                        rgba(232, 232, 232, 0.2) 0%,
                        #e8e8e8 53.78%,
                        rgba(232, 232, 232, 0.2) 100%);
                opacity: 1;
                position: absolute;
                display: block;
                top: 0;
                left: 50%;
                transform: translateX(-50%);
            }
        }

        .cs-li {
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            list-style: none;
            line-height: 1.5em;
            font-weight: 400;
            text-align: inherit;
            margin: 0;
            padding: 0;
            color: var(--bodyTextColor);
            display: block;
        }

        .cs-button-transparent {
            font-size: calc(16 / 16 * 1rem);
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-decoration: none;
            font-weight: 700;
            text-align: center;
            margin: 0;
            color: var(--primary);
            min-width: calc(150 / 16 * 1rem);
            padding: 0 calc(24 / 16 * 1rem);
            background-color: transparent;
            border: 1px solid var(--primary);
            display: inline-block;
            position: relative;
            z-index: 1;
            /* prevents padding from adding to the width */
            box-sizing: border-box;
            transition:
                color 0.3s,
                border-color 0.3s,
                background-color 0.3s;

            &:before {
                content: "";
                position: absolute;
                height: 100%;
                width: 0%;
                background: #000;
                opacity: 1;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width 0.3s;
            }

            &:hover {
                color: #fff;
                border-color: #000;
                background-color: #000;

                &:before {
                    width: 100%;
                }
            }
        }

        .cs-button-transparent {
            /* this is the section specific override if you choose to pull out the cs-button-transparent and place it in a global stylesheet to resue acorss the entire site */
            /* 28px - 40px */
            margin-top: clamp(1.75rem, 3vw, 2.5rem);
        }

        .cs-floater {
            width: calc(652 / 16 * 1rem);
            height: auto;
            position: absolute;
            top: 0;
            right: 0;
            z-index: -1;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #pricing-1319 {
        .cs-container {
            max-width: calc(1440 / 16 * 1rem);

            .cs-card-group {
                margin-bottom: calc(72 / 16 * 1rem);
                flex-direction: row;
                align-items: stretch;
            }

            .cs-item {
                &:nth-of-type(2) {
                    transform: translateY(4.5rem);
                }
            }
        }
    }
}

/* Large Desktop Desktop - 1300px */
@media only screen and (min-width: 81.25rem) {
    #pricing-1319 {
        .cs-container {
            flex-direction: row;
            justify-content: space-between;
        }

        .cs-content {
            text-align: left;
            align-items: flex-start;
            /* sends it to the right in the 2nd position */
            order: 2;
        }

        .cs-card-group {
            width: 65.7%;
            /* prevents flexbox from squishing it */
            flex: none;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #pricing-1319 {
            background-color: rgba(0, 0, 0, 0.2);

            .cs-title,
            .cs-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-text {
                opacity: 0.8;
            }

            .cs-ul {
                &:before {
                    opacity: 0.5;
                }
            }

            .cs-item {
                background-color: var(--medium);

                &.cs-popular {
                    background-color: var(--primary);

                    .cs-ul {
                        &:before {
                            opacity: 1;
                            filter: grayscale(1) brightness(20%);
                        }
                    }

                    .cs-package,
                    .cs-desc,
                    .cs-price,
                    .cs-duration,
                    .cs-li {
                        color: var(--headerColor);
                    }

                    .cs-button-transparent {
                        background-color: #1a1a1a;
                        color: var(--primary);
                    }
                }
            }

            .cs-package {
                color: var(--primary);
            }

            .cs-desc,
            .cs-price,
            .cs-duration,
            .cs-li {
                color: var(--bodyTextColorWhite);
            }

            .cs-duration,
            .cs-li,
            .cs-desc {
                opacity: 0.8;
            }

            .cs-floater {
                opacity: 0.3;
            }
        }
    }
}