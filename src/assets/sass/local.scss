/*-- -------------------------- -->
<---          Gallery           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #gallery-43 {
        padding: var(--sectionPadding);
        position: relative;
        overflow: hidden;

        .cs-container {
            width: 100%;
            max-width: 69rem;
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(3rem, 6vw, 4rem);
        }

        .cs-content {
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .cs-title {
            max-width: 63.75rem;
        }

        .cs-text {
            max-width: 50.625rem;
        }

        .cs-image-group {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: clamp(1rem, 1.5vw, 1.25rem);
        }

        .cs-item {
            width: 100%;
            height: 100%;
            aspect-ratio: 1;
            margin: 0;
            position: relative;
            display: block;

            &:hover {
                .cs-hover-box {
                    opacity: 1;
                }

                .cs-icon {
                    transform: rotateY(0);
                }

                .cs-h3,
                .cs-hover-box-text {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        }

        .cs-picture {
            margin: auto;
            width: 100%;
            height: 100%;
            display: block;
            position: relative;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                object-fit: cover;
            }
        }

        .cs-hover-box {
            text-align: center;
            width: 100%;
            height: 100%;
            padding: 1em;
            background-color: rgba(79, 139, 186, 0.9);
            opacity: 0;
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            perspective: 700px;
            top: 0;
            left: 0;
            z-index: 1;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        .cs-icon {
            width: clamp(3.125rem, 5vw, 3.75rem);
            height: clamp(3.125rem, 5vw, 3.75rem);
            margin-bottom: clamp(1rem, 5vw, 2rem);
            border-radius: 50%;
            background-color: #fff;
            outline: 0.5rem solid rgba(255, 255, 255, 0.7);
            transform: rotateY(90deg);
            position: relative;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            flex: none;
            transition: transform 0.5s;

            img {
                width: 1.25rem;
                height: 1.25rem;
            }
        }

        .cs-h3 {
            font-size: clamp(1.25rem, 2vw, 1.5625rem);
            line-height: 1.2em;
            font-weight: 700;
            margin: 0 auto;
            max-width: 16.875rem;
            color: #fff;
            opacity: 0;
            transform: translateY(0.625rem);
            margin-bottom: 0.5rem;
            transition: opacity 0.3s, transform 0.3s ease-out;
            transition-delay: 0.1s;
        }

        .cs-hover-box-text {
            font-size: clamp(0.8125rem, 1vw, 1rem);
            line-height: 1.5em;
            margin: 0 auto;
            max-width: 16.875rem;
            color: #fff;
            opacity: 0;
            transform: translateY(0.625rem);
            transition: opacity 0.3s, transform 0.3s ease-out;
            transition-delay: 0.2s;
        }
    }
}

/* In Between - 600px */
@media only screen and (min-width: 37.5rem) {
    #gallery-43 {
        .cs-image-group {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            grid-template-rows: 1fr;
        }

        .cs-item {
            grid-column: span 6;
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #gallery-43 .cs-item {
        grid-column: span 4;
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #gallery-43 {

            .cs-title,
            .cs-text {
                color: var(--bodyTextColorWhite);
            }

            .cs-text {
                opacity: 0.8;
            }
        }
    }
}

/*-- -------------------------- -->
<---   Side By Side Reverse     -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #sbsr-2289 {
        padding: var(--sectionPadding);
        overflow: hidden;
        background-color: #f8fafc;

        .cs-container {
            width: 100%;
            /* changes to 1280px at desktop */
            max-width: calc(584/16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            gap: calc(48/16 * 1rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: left;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: flex-start;
            position: relative;
            z-index: 10;
        }

        .cs-text {
            margin-bottom: calc(16/16 * 1rem);

            &:last-of-type {
                margin-bottom: calc(32/16 * 1rem);
            }
        }

        .cs-features {
            width: 100%;
            margin: 0 0 calc(32/16 * 1rem);
            /* 24px - 32px */
            padding: clamp(1.5rem, 4vw, 2rem);
            background-color: #fff;
            border-left: calc(4/16 * 1rem) solid var(--secondary);
        }

        .cs-h3 {
            font-size: calc(16/16 * 1rem);
            font-weight: 700;
            line-height: 1.5em;
            margin: 0 0 calc(12/16 * 1rem);
            color: var(--headerColor);
        }

        .cs-ul {
            font-size: calc(12/16 * 1rem);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: calc(12/16 * 1rem);
        }

        .cs-li {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            list-style: none;
            /* 20px - 24px */
            padding-left: clamp(1.25rem, 3vw, 1.5rem);
            color: var(--bodyTextColor);
            position: relative;

            &::before {
                content: "";
                width: calc(8/16 * 1rem);
                height: calc(8/16 * 1rem);
                margin-top: calc(8/16 * 1rem);
                background-color: var(--secondary);
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                transform: rotate(45deg);
            }
        }

        .cs-button-solid {
            font-size: calc(16/16 * 1rem);
            font-weight: 700;
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-align: center;
            text-decoration: none;
            min-width: calc(150/16 * 1rem);
            margin: 0;
            /* prevents padding from adding to the width */
            box-sizing: border-box;
            padding: 0 calc(24/16 * 1rem);
            background-color: var(--primary);
            color: #1a1a1a;
            display: inline-block;
            position: relative;
            z-index: 1;
            transition: color 0.3s;

            &:before {
                content: "";
                width: 0%;
                height: 100%;
                background: #000;
                opacity: 1;
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width 0.3s;
            }

            &:hover {
                color: #fff;

                &:before {
                    width: 100%;
                }
            }
        }

        .cs-image-group {
            width: 100%;
            height: 126vw;
            max-height: calc(626/16 * 1rem);
            display: block;
            order: -1;
            position: relative;
            z-index: 1;
        }

        .cs-picture {
            /* big background image */
            width: 100%;
            display: block;
            position: absolute;
            top: 0;
            bottom: calc(95/16 * 1rem);
            left: 0;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                position: absolute;
                top: 0;
                left: 0;
            }
        }

        .cs-box {
            text-align: left;
            width: 91%;
            max-width: calc(450/16 * 1rem);
            /* prevents padding and border from affecting height and width */
            box-sizing: border-box;
            /* 24px - 32px */
            padding: clamp(1.5rem, 3vw, 2rem);
            background-color: #fffcf3;
            display: inline-flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            column-gap: calc(24/16 * 1rem);
            row-gap: calc(8/16 * 1rem);
            position: absolute;
            right: 50%;
            bottom: 0;
            z-index: 10;
            transform: translate(50%);
        }

        .cs-number {
            /* 31px - 49px */
            font-size: clamp(1.9375rem, 4vw, 3.0625rem);
            font-weight: 700;
            line-height: 1.2em;
            text-transform: uppercase;
            margin: 0;
            color: var(--General-Secondary, #ffc219);
        }

        .cs-heading {
            font-size: calc(20/16 * 1rem);
            font-weight: 700;
            line-height: 1.2em;
            margin: 0 0 calc(8/16 * 1rem);
            color: var(--headerColor);
        }

        .cs-desc {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
        }

        .cs-graphic {
            width: 150%;
            height: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            z-index: -1;
            transform: translate(-50%, -50%);
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #sbsr-2289 {
        .cs-box {
            flex-direction: row;
            align-items: center;
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #sbsr-2289 {
        .cs-container {
            max-width: calc(1280/16 * 1rem);
            flex-direction: row;
            align-items: stretch;
            gap: calc(20/16 * 1rem);
        }

        .cs-content {
            padding-right: calc(52/16 * 1rem);
            padding-bottom: calc(108/16 * 1rem);
        }

        .cs-image-group {
            height: auto;
            min-height: calc(642/16 * 1rem);
            max-height: 100%;
            order: initial;
        }

        .cs-picture {
            bottom: calc(64/16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #sbsr-2289 {
            background-color: var(--darkModeBackground);

            .cs-title,
            .cs-text,
            .cs-h3,
            .cs-li,
            .cs-heading,
            .cs-desc {
                color: var(--bodyTextColorWhite);
            }

            .cs-text,
            .cs-li,
            .cs-desc {
                opacity: 0.8;
            }

            .cs-features,
            .cs-box {
                background-color: var(--medium);
            }
        }
    }
}

/*-- -------------------------- -->
<---          GALLERY           -->
<--- -------------------------- -*/

@media only screen and (min-width: 0rem) {
    #gallery-2297 {
        padding: var(--sectionPadding);
        overflow: hidden;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-container {
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-content {
        width: 100%;
        display: contents;
    }

    #gallery-2297 .cs-title {
        margin: 0 0 3rem;
    }

    #gallery-2297 .cs-text {
        max-width: 50.625rem;
    }

    #gallery-2297 .cs-button-group {
        margin: 2rem auto 0;
        display: flex;
        order: 2;
        column-gap: clamp(1.25rem, 3.4vw, 2.5rem);
        row-gap: 1rem;
    }

    #gallery-2297 .cs-button {
        padding: 1rem;
        background-color: #f3f3f3;
        border: none;
        transition: background-color 0.3s;
    }

    #gallery-2297 .cs-button:hover {
        background-color: var(--primary);
        cursor: pointer;
    }

    #gallery-2297 .cs-button:hover .cs-arrow {
        filter: invert(1) brightness(1000%);
    }

    #gallery-2297 .cs-arrow {
        width: 1.25rem;
        height: 1.25rem;
        display: block;
    }

    #gallery-2297 .cs-gallery-wrapper {
        width: 100%;
        position: relative;
        z-index: 1;
    }

    #gallery-2297 .cs-gallery {
        width: 100%;
        margin: 0;
        padding: 0;
        display: grid;
        grid-template-columns: repeat(12, 1fr);
        gap: clamp(1rem, 1.5vw, 1.25rem);
        position: relative;
        transform-style: preserve-3d;
        perspective: 700px;
        transition: transform 0.7s, opacity 0.3s, visibility 0.5s, top 0.3s, left 0.3s;
        transform-origin: left top;
    }

    #gallery-2297 .cs-gallery.cs-hidden {
        pointer-events: none;
        opacity: 0;
        visibility: hidden;
        position: absolute;
        top: 0;
        left: 0;
        transform: scaleY(0) scaleX(0);
    }

    #gallery-2297 .cs-gallery.cs-hidden .cs-image {
        opacity: 0;
        transform: translateY(2.1875rem) rotateX(90deg);
    }

    #gallery-2297 .cs-image {
        min-height: clamp(20rem, 20vw, 32.75rem);
        overflow: hidden;
        opacity: 1;
        display: block;
        grid-column: span 12;
        position: relative;
        transform: translateY(0rem) rotateX(0);
        transition: opacity 0.6s, transform 0.6s;
    }

    #gallery-2297 .cs-image:nth-of-type(1) {
        transition-delay: 0.1s;
    }

    #gallery-2297 .cs-image:nth-of-type(2) {
        transition-delay: 0.2s;
    }

    #gallery-2297 .cs-image:nth-of-type(3) {
        transition-delay: 0.3s;
    }

    #gallery-2297 .cs-picture {
        width: 100%;
        height: 100%;
        background-color: #000;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
    }

    #gallery-2297 .cs-picture img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
        transition: transform 0.65s, opacity 0.3s;
    }

    #gallery-2297 .cs-info {
        width: 90%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 10;
    }

    #gallery-2297 .cs-tag {
        font-size: clamp(0.8125rem, 2vw, 1rem);
        font-weight: 700;
        text-transform: uppercase;
        padding: 0.5rem 1.25rem;
        background-color: var(--primary);
        color: var(--bodyTextColorWhite);
    }

    #gallery-2297 .cs-project {
        font-size: 1.25rem;
        font-weight: 700;
        text-transform: uppercase;
        padding: 0.75rem 1rem;
        background-color: #fff;
        color: var(--headerColor);
    }

    #gallery-2297 .cs-stats {
        max-width: 80rem;
        margin: clamp(3rem, 9vw, 3.5rem) auto 0;
        display: flex;
        flex-direction: column;
        column-gap: clamp(1rem, 3vw, 1.5rem);
        row-gap: 2rem;
    }

    #gallery-2297 .cs-stat {
        display: flex;
        flex-direction: column;
        column-gap: 1.25rem;
        row-gap: 0.5rem;
    }

    #gallery-2297 .cs-number {
        font-size: var(--headerFontSize);
        font-weight: 700;
        line-height: 1.2em;
        color: var(--primary);
    }

    #gallery-2297 .cs-desc {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.2em;
        color: var(--headerColor);
    }
}

@media only screen and (min-width: 48rem) {
    #gallery-2297 .cs-container {
        gap: clamp(3rem, 6vw, 4rem);
    }

    #gallery-2297 .cs-content {
        text-align: left;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 1.5rem;
    }

    #gallery-2297 .cs-title {
        margin: 0;
    }

    #gallery-2297 .cs-button-group {
        margin: 0;
    }

    #gallery-2297 .cs-image {
        grid-column: span 4;
    }

    #gallery-2297 .cs-stats {
        flex-direction: row;
    }

    #gallery-2297 .cs-stat {
        flex: 1;
    }
}

@media only screen and (min-width: 64rem) {
    #gallery-2297 .cs-content {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-end;
        gap: 4rem;
    }

    #gallery-2297 .cs-image:hover .cs-tag,
    #gallery-2297 .cs-image:hover .cs-project {
        opacity: 1;
        transform: translateX(0);
    }

    #gallery-2297 .cs-image:hover .cs-picture img {
        opacity: 0.2;
        transform: scale(1.1);
    }

    #gallery-2297 .cs-tag {
        opacity: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out, opacity 0.3s;
    }

    #gallery-2297 .cs-project {
        opacity: 0;
        transform: translateX(-100%);
        transition: transform 0.4s ease-out, opacity 0.3s;
        transition-delay: 0.1s;
    }
}

@media only screen and (min-width: 81.25rem) {
    #gallery-2297 .cs-stat {
        flex-direction: row;
        align-items: center;
    }
}

/*-- -------------------------- -->
<---        Side By Side        -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #sbs-2295 {
        padding: var(--sectionPadding);
        background-color: #f8fafc;
        overflow: hidden;
        position: relative;
        z-index: 1;

        .cs-container {
            width: 100%;
            /* changes to 1280px at tablet */
            max-width: calc(704/16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 48px - 64px */
            gap: clamp(3rem, 4vw, 4rem);
        }

        .cs-image-group {
            /* scales the whole section down and ties the font size to the vw and stops at 75% of the vale of 1em, changes at desktop */
            font-size: min(1.98vw, .826rem);
            /* everything inside this box is in ems so we can scale it all down proportionally with a font size */
            width: calc(738/16 * 1em);
            height: calc(578/16 * 1em);
            order: -1;
            position: relative;
            z-index: 1;
            /* prevents flexbox from squishing it */
            flex: none;
        }

        .cs-picture {
            width: 100%;
            height: 100%;
            border: calc(16/16 * 1em) solid var(--bodyTextColorWhite);
            display: block;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                top: 0;
                left: 0;
            }
        }

        .cs-picture-wrapper1 {
            width: calc(412/16 * 1em);
            height: calc(457/16 * 1em);
            display: flex;
            flex-direction: column;
            gap: calc(16/16 * 1rem);
            position: absolute;
            top: 0;
            left: 0;
        }

        .cs-picture-wrapper2 {
            width: calc(413/16 * 1em);
            height: calc(474/16 * 1em);
            display: flex;
            flex-direction: column-reverse;
            gap: calc(16/16 * 1rem);
            position: absolute;
            right: 0;
            bottom: 0;
        }

        .cs-tag {
            /* 14px - 24px */
            font-size: clamp(0.875rem, 1.4vw, 1.5rem);
            font-weight: 700;
            line-height: 1.2em;
            text-align: center;
            color: var(--headerColor);
        }

        .cs-arrow {
            width: calc(148/16 * 1em);
            height: calc(54/16 * 1em);
            position: absolute;
            top: calc(242/16 * 1em);
            left: calc(255/16 * 1em);
            transform: rotate(30deg);
        }

        .cs-content {
            /* set text align to center if content needs to be centrally aligned */
            text-align: left;
            width: 100%;
            max-width: calc(586/16 * 1rem);
            display: flex;
            flex-direction: column;
            /* aligns content to the left, set to center to centrally align */
            align-items: flex-start;
        }

        .cs-title {
            max-width: 30ch;
        }

        .cs-text {
            margin-bottom: calc(16/16 * 1rem);

            &:last-of-type {
                margin-bottom: calc(24/16 * 1rem);
            }
        }

        .cs-button-solid {
            font-size: calc(16/16 * 1rem);
            font-weight: 700;
            /* 46px - 56px */
            line-height: clamp(2.875rem, 5.5vw, 3.5rem);
            text-align: center;
            text-decoration: none;
            min-width: calc(150/16 * 1rem);
            margin: 0;
            padding: 0 calc(24/16 * 1rem);
            background-color: var(--primary);
            color: var(--headerColor);
            display: inline-block;
            position: relative;
            z-index: 1;

            &:before {
                content: "";
                width: 0%;
                height: 100%;
                background: #000;
                opacity: 1;
                border-radius: calc(4/16 * 1rem);
                position: absolute;
                top: 0;
                left: 0;
                z-index: -1;
                transition: width 0.3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }
        }

        .cs-h3 {
            font-size: calc(16/16 * 1rem);
            font-weight: 700;
            line-height: 1.5em;
            margin: 0;
            color: var(--headerColor);
        }

        .cs-ul {
            margin: calc(12/16 * 1rem) 0 calc(32/16 * 1rem);
            padding: 0;
            display: flex;
            flex-direction: column;
            gap: calc(12/16 * 1rem);
        }

        .cs-li {
            font-size: calc(16/16 * 1rem);
            line-height: 1.5em;
            list-style: none;
            /* 20px - 24px */
            padding-left: clamp(1.25rem, 3vw, 1.5rem);
            color: var(--bodyTextColor);
            position: relative;

            strong {
                color: #1a1a1a;
            }

            &::before {
                content: "";
                width: calc(8/16 * 1rem);
                height: calc(8/16 * 1rem);
                margin-top: calc(8/16 * 1rem);
                background-color: var(--secondary);
                display: block;
                position: absolute;
                top: 0;
                left: 0;
                transform: rotate(45deg);
            }
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #sbs-2295 {
        .cs-container {
            max-width: calc(1280/16 * 1rem);
        }

        .cs-image-group {
            order: initial;
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #sbs-2295 {
        .cs-container {
            flex-direction: row;
            align-items: stretch;
        }

        .cs-image-group {
            font-size: min(1.03vw, 1rem);
            height: auto;
        }

        .cs-picture-wrapper1 {
            height: auto;
            bottom: calc(121/16 * 1em);
        }

        .cs-picture-wrapper2 {
            height: auto;
            top: calc(104/16 * 1em);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #sbs-2295 {
            background-color: rgba(0, 0, 0, 0.2);

            .cs-title,
            .cs-text,
            .cs-h3,
            .cs-li,
            .cs-tag {
                color: var(--bodyTextColorWhite);
            }

            .cs-text,
            .cs-li {
                opacity: 0.8;

                strong {
                    color: var(--bodyTextColorWhite);
                }
            }

            .cs-picture {
                border-color: var(--medium);
            }
        }
    }
}