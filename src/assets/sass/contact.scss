/* PAGE-<PERSON><PERSON><PERSON><PERSON> STYLES FOR THE CONTACT PAGE */

/*-- -------------------------- -->
<---          Contact           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    :root {
        /* 13px - 16px */
        --topperFontSize: clamp(0.8125rem, 1.6vw, 1rem);
        /* 31px - 49px */
        --headerFontSize: clamp(1.9375rem, 4.5vw, 3.0625rem);
        --bodyFontSize: 1rem;

        /* 60px - 100px top and bottom */
        --sectionPadding: clamp(3.75em, 7.82vw, 6.25em) 1rem;
    }

    #cs-contact {
        padding: var(--sectionPadding);
        font-family: "Maven Pro", "Arial", sans-serif;

        .cs-container {
            margin: auto;
            /* changes to 1280px at desktop */
            max-width: calc(800 / 16 * 1rem);
            width: 100%;
        }

        .cs-content {
            width: 100%;
        }

        .cs-topper {
            /* 8px - 12px */
            margin-bottom: clamp(0.5rem, 1.4vw, 0.75rem);
            text-align: left;
        }

        .cs-title {
            max-width: calc(800 / 16 * 1rem);
            text-align: left;
        }

        .cs-text {
            margin: 0 auto 0 0;
            /* 40px - 48px */
            margin-bottom: clamp(2.5rem, 6.3vw, 3rem);
            /* 404px - 522px */
            max-width: clamp(25.25rem, 30vw, 32.625rem);
            text-align: left;
        }

        #cs-form {
            margin-bottom: calc(40 / 16 * 1rem);
            label {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: flex-start;
                /* 12px - 20px */
                margin-bottom: clamp(0.75rem, 1.3em, 1.25rem);
                font-size: calc(16 / 16 * 1rem);
                line-height: 1.5em;
                font-weight: 700;
                color: var(--headerColor);
            }

            .cs-label-message {
                /* 32px - 48px */
                margin-bottom: clamp(2rem, 6.3vw, 3rem);
            }

            input,
            textarea {
                border: 1px solid #b4b2c7;
                border-radius: calc(8 / 16 * 1rem);
                margin-top: calc(4 / 16 * 1rem);
                height: calc(64 / 16 * 1rem);
                width: 100%;
                /* prevents border & padding from affecting height */
                box-sizing: border-box;
                padding-left: calc(20 / 16 * 1rem);
                font-size: calc(16 / 16 * 1rem);
                transition: border 0.3s;

                &:hover {
                    border: 1px solid var(--primary);
                }
            }

            textarea {
                min-height: calc(120 / 16 * 1rem);
                padding-top: calc(20 / 16 * 1rem);
                font-family: inherit;
            }

            .cs-button-solid {
                border: none;
                width: 100%;
            }
        }

        .cs-right-section {
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-start;
            border-radius: calc(8 / 16 * 1rem);
            height: calc(320 / 16 * 1rem);
            /* cuts off corners on img tag */
            overflow: hidden;
            /* 24px - 40px top & bottom */
            /* 20px - 40px left & right */
            padding: clamp(1.5rem, 3vw, 2.5rem) clamp(1.25rem, 3vw, 2.5rem);
        }

        .cs-header {
            display: block;
            margin-bottom: calc(8 / 16 * 1rem);
            /* 16px - 20px */
            font-size: clamp(1rem, 3vw, 1.25rem);
            line-height: 1.2em;
            font-weight: 700;
            color: var(--bodyTextColorWhite);
        }

        .cs-link {
            position: relative;
            display: block;
            margin-bottom: calc(20 / 16 * 1rem);
            text-decoration: none;
            /* 16px - 20px */
            font-size: clamp(1rem, 3vw, 1.25rem);
            line-height: 1.2em;
            color: var(--bodyTextColorWhite);

            &:before {
                position: absolute;
                bottom: calc(-2 / 16 * 1rem);
                left: 0;
                /* Animated underline */
                content: "";
                opacity: 1;
                display: block;
                /* current color of the parent */
                background: currentColor;
                height: 2px;
                width: 0%;
                transition: width 0.3s;
            }

            &:hover {
                &:before {
                    width: 100%;
                }
            }

            &:last-of-type {
                margin-bottom: 0;
            }
        }

        .cs-block {
            /* sends second address line to the bottom on its own line */
            display: block;
        }

        .cs-bg-picture {
            z-index: -1;
            position: absolute;
            top: 0;
            left: 0;
            display: block;
            height: 100%;
            width: 100%;
            transition: transform 0.6s;

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                /* Makes img tag act as a background image */
                object-fit: cover;
            }

            &:before {
                z-index: 1;
                position: absolute;
                top: 0;
                left: 0;
                /* background color overlay */
                content: "";
                display: block;
                background: linear-gradient(
                    180deg,
                    rgba(0, 0, 0, 0) 0%,
                    rgba(0, 0, 0, 0.8) 100%
                );
                height: 100%;
                width: 100%;
            }
        }
    }
}

/* Tablet - 700px */
@media only screen and (min-width: 43.75em) {
    #cs-contact {
        #cs-form {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: flex-start;

            label {
                width: 48%;
            }

            .cs-label-message {
                width: 100%;
            }
        }
    }
}

/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #cs-contact {
        .cs-container {
            display: flex;
            justify-content: flex-end;
            align-items: flex-start;
            max-width: calc(1280 / 16 * 1rem);
            gap: calc(80 / 16 * 1rem);
        }

        #cs-form {
            max-width: calc(630 / 16 * 1rem);
            label {
                width: 100%;
            }
        }

        .cs-right-section {
            /* changes to 578px at 1300px wide */
            height: calc(686 / 16 * 1rem);
            max-width: calc(542 / 16 * 1rem);
            width: 40%;
            /* prevents flexbox from squishing it */
            flex: none;

            &:hover {
                .cs-bg-picture {
                    transform: scale(1.1);
                }
            }
        }

        .cs-block {
            /* goes back to inline so it stays "in line" with the rest of the text */
            display: inline-block;
        }
    }
}

/* Small Desktop - 1300px */
@media only screen and (min-width: 81.25em) {
    #cs-contact {
        #cs-form {
            column-gap: calc(0 / 16 * 1rem);
            label {
                max-width: calc(305 / 16 * 1rem);
                width: 48%;
            }

            .cs-label-message {
                max-width: 100%;
            }
        }

        .cs-right-section {
            height: calc(578 / 16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #cs-contact {
            .cs-text,
            .cs-title {
                color: var(--bodyTextColorWhite);
            }

            #cs-form {
                label,
                input,
                textarea {
                    background-color: transparent;
                    color: var(--bodyTextColorWhite);

                    &::placeholder {
                        /* lighten up the color of the text by 40%*/
                        filter: brightness(1.4);
                    }
                }
            }

            .cs-bg-picture {
                background-color: #000;

                img {
                    /* lets parent background-color bleed through and darken it */
                    opacity: 0.5;
                }
            }
        }
    }
}

/*-- -------------------------- -->
<---            FAQ             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #faq-1108 {
        padding: var(--sectionPadding);
        position: relative;

        .cs-container {
            width: 100%;
            /* changes to 1280px at desktop */
            max-width: calc(584 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 32px - 48px */
            gap: clamp(2rem, 6vw, 3rem);
        }

        .cs-content {
            /* set text align to left if content needs to be left aligned */
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            /* centers content horizontally, set to flex-start to left align */
            align-items: center;
        }

        .cs-title {
            /* 32px - 48px */
            margin: 0 0 clamp(2rem, 5vw, 3rem);
        }

        .cs-picture {
            width: 100%;
            /* 360px - 400px */
            height: clamp(22.5rem, 54vw, 25rem);
            display: block;
            position: relative;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }

        .cs-faq-group {
            width: 100%;
            max-width: calc(650 / 16 * 1rem);
            padding: 0;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: calc(12 / 16 * 1rem);
        }

        .cs-faq-item {
            list-style: none;
            width: 100%;
            background-color: #f7f7f7;
            /* clips all corners of the button that overlap the rounded border */
            overflow: hidden;
            transition: border-bottom 0.3s;

            &.active {
                .cs-button {
                    color: var(--primary);

                    &:before {
                        background-color: var(--primary);
                        transform: rotate(315deg);
                    }

                    &:after {
                        background-color: var(--primary);
                        transform: rotate(-315deg);
                    }
                }

                .cs-item-p {
                    height: auto;
                    /* 20px - 24px bottom */
                    /* 16px - 24px left & right */
                    padding: 0 clamp(1rem, 2vw, 1.5rem) clamp(1.25rem, 1.3vw, 1.5rem);
                    opacity: 1;
                }
            }
        }

        .cs-button {
            /* 16px - 20px */
            font-size: clamp(1rem, 2.5vw, 1.25rem);
            line-height: 1.2em;
            text-align: left;
            font-weight: bold;
            /* 20px - 24px */
            padding: clamp(1.25rem, 2vw, 1.5rem);
            background-color: #f7f7f7;
            border: none;
            color: var(--headerColor);
            display: block;
            width: 100%;
            position: relative;
            transition:
                background-color 0.3s,
                color 0.3s;

            &:hover {
                cursor: pointer;
            }

            &:before {
                /* left line */
                content: "";
                width: calc(8 / 16 * 1rem);
                height: calc(2 / 16 * 1rem);
                background-color: var(--headerColor);
                opacity: 1;
                border-radius: 50%;
                position: absolute;
                display: block;
                top: 50%;
                right: calc(24 / 16 * 1rem);
                transform: rotate(45deg);
                /* animate the transform from the left side of the x axis, and the center of the y */
                transform-origin: left center;
                transition: transform 0.5s;
            }

            &:after {
                /* right line */
                content: "";
                width: calc(8 / 16 * 1rem);
                height: calc(2 / 16 * 1rem);
                background-color: var(--headerColor);
                opacity: 1;
                border-radius: 50%;
                position: absolute;
                display: block;
                top: 50%;
                right: calc(21 / 16 * 1rem);
                transform: rotate(-45deg);
                /* animate the transform from the right side of the x axis, and the center of the y */
                transform-origin: right center;
                transition: transform 0.5s;
            }
        }

        .cs-button-text {
            width: 80%;
            display: block;
        }

        .cs-item-p {
            /* 14px - 16px */
            font-size: clamp(0.875rem, 1.5vw, 1rem);
            line-height: 1.5em;
            width: 90%;
            height: 0;
            margin: 0;
            /* 16px - 24px */
            padding: 0 clamp(1rem, 2vw, 1.5rem);
            opacity: 0;
            color: var(--bodyTextColor);
            /* clips the text so it doesn't show up */
            overflow: hidden;
            transition:
                opacity 0.3s,
                padding-bottom 0.3s;
        }
    }
}

/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #faq-1108 {
        .cs-container {
            max-width: calc(1280 / 16 * 1rem);
            flex-direction: row;
            justify-content: space-between;
            align-items: stretch;
        }

        .cs-content {
            width: 40%;
            text-align: left;
            align-items: flex-start;
            /* prevents flexbox from squishing it */
            flex: none;
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #faq-1108 {

            .cs-title,
            .cs-item-p {
                color: var(--bodyTextColorWhite);
            }

            .cs-faq-item {
                background-color: var(--accent);

                &.active {
                    .cs-button {
                        background-color: var(--primary);
                        color: var(--bodyTextColorWhite);

                        &:before,
                        &:after {
                            background-color: var(--bodyTextColorWhite);
                        }
                    }

                    .cs-item-p {
                        /* 20px - 24px */
                        padding-top: clamp(1.25rem, 1.3vw, 1.5rem);
                    }
                }
            }

            .cs-button {
                background-color: var(--accent);
                color: var(--bodyTextColorWhite);

                &:before,
                &:after {
                    background-color: var(--bodyTextColorWhite);
                }
            }
        }
    }
}