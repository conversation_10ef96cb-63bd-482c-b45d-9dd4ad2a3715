/*-- -------------------------- -->
<---          Gallery           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #gallery-1449 {
        padding: var(--sectionPadding);

        .cs-container {
            width: 100%;
            max-width: calc(1280 / 16 * 1rem);
            margin: auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* 48px - 64px */
            gap: clamp(3rem, 6vw, 4rem);
            position: relative;
            z-index: 1;
        }

        .cs-content {
            text-align: center;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: calc(24 / 16 * 1rem);
        }

        .cs-title {
            margin: 0;
        }

        .cs-button-group {
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            row-gap: calc(16 / 16 * 1rem);
            /* 20px - 32px */
            column-gap: clamp(1.25rem, 2vw, 2rem);
        }

        .cs-button {
            font-size: calc(16 / 16 * 1rem);
            line-height: 1.5em;
            color: var(--bodyTextColor);
            background-color: transparent;
            border: none;
            position: relative;
            z-index: 1;
            transition: color 0.3s;

            &:before {
                content: '';
                width: 100%;
                height: 4px;
                background: var(--primary);
                opacity: 0;
                border-radius: 4px 4px 0 0;
                position: absolute;
                display: block;
                bottom: 0;
                left: 0;
                transition: opacity .3s, bottom .3s;
            }

            &:hover {
                color: var(--primary);
                cursor: pointer;

                &:before {
                    opacity: 1;
                    bottom: calc(-10 / 16 * 1rem);
                }
            }

            &.cs-active {
                color: var(--primary);

                &:before {
                    opacity: 1;
                    bottom: calc(-10 / 16 * 1rem);
                }
            }
        }

        .cs-gallery {
            width: 100%;
            padding: 0;
            margin: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(19.0625rem, 1fr));
            /* 16px - 20px */
            gap: clamp(1rem, 1.5vw, 1.25rem);
            position: relative;
            perspective: 700px;
            transform-style: preserve-3d;
            transition:
                transform 0.7s,
                opacity 0.3s,
                visibility 0.5s,
                top 0.3s,
                left 0.3s;
            /* makes the transfrom scaling orgin the top left corner, dictates the direction by which the scale transforms animate towards */
            transform-origin: left top;

            &.cs-hidden {
                position: absolute;
                /* by using visibility:hidden instead of display:none, we can see the animations from the opacity and transforms, display:none won't render animations. */
                visibility: hidden;
                /* prevents the mouse from interacting with it */
                pointer-events: none;
                /* hidden galleries have a 0 opacity, and we animate the opacity to 1 when they become active */
                opacity: 0;
                /* this top and left value help control the animation, by setting it to position absolute and left 0, the gallery won't fly off screen to the left, it will stop its position to be at the left edge of the .cs-container (left: 0). Same for the bottom:0 value, the gallery won't go past that posiiton when it animates */
                bottom: 0;
                left: 0;
                position: absolute;
                /* prevents the hidden galleries from overflowing the section, and makes a nice animations to transition to and from */
                transform: scaleY(0) scaleX(0);

                .cs-image {
                    /* when gallery is hidden, add these styles to the cs-image to animate from when cs-hidden is removed from the .cs-gallery */
                    transform: translateY(2.1875rem) rotateX(90deg);
                    opacity: 0;
                }
            }
        }

        .cs-image {
            /* 260px - 360px */
            min-height: clamp(16.25rem, 60vw, 20rem);
            border-radius: calc(16 / 16 * 1rem);
            /* clips the image corners */
            overflow: hidden;
            display: block;
            position: relative;
            /* when .cs-hidden is removed from the .cs-gallery, reset these values and animate between their hidden styles */
            transform: translateY(0rem) rotateX(0);
            opacity: 1;
            transition:
                opacity 0.6s,
                transform 0.6s;

            &:nth-of-type(1) {
                /* the transition delays change at desktop */
                /* these delays stagger the reveal of each image so they all go one after the other and not all at once */
                transition-delay: 0.1s;
            }

            &:nth-of-type(2) {
                transition-delay: 0.2s;
            }

            &:nth-of-type(3) {
                transition-delay: 0.3s;
            }

            &:nth-of-type(4) {
                transition-delay: 0.4s;
            }

            &:nth-of-type(5) {
                transition-delay: 0.5s;
            }

            &:nth-of-type(6) {
                transition-delay: 0.6s;
            }

            &:nth-of-type(7) {
                transition-delay: 0.7s;
            }

            &:nth-of-type(8) {
                transition-delay: 0.8s;
            }

            img {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                width: 100%;
                /* makes it act like a background image */
                object-fit: cover;
            }
        }
    }
}

/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #gallery-1449 {
        .cs-content {
            text-align: left;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;
        }

        .cs-button {
            margin-bottom: calc(10 / 16 * 1rem);
        }
    }
}

/* Dark Mode */
@media only screen and (min-width: 0rem) {
    body.dark-mode {
        #gallery-1449 {
            .cs-title, .cs-button {
                color: var(--bodyTextColorWhite);
            }
        }
    }
}