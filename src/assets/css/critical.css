/*-- -------------------------- -->
<---           Hero             -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #hero-143 {
    /* Centers button */
    text-align: center;
    /* 144px - 300px - leaving extra space for the navigation */
    padding: clamp(9rem, 25.95vw, 18.75rem) 1rem 0;
    /* 130px - 450px */
    padding-bottom: clamp(12.125rem, 30.95vw, 28.125rem);
    position: relative;
    z-index: 1;
    /* Prevents white rectangle pseudos from overlapping the sections below */
    overflow: hidden;
  }
  #hero-143:before {
    /* Left side of the triangle */
    content: "";
    width: 31.25rem;
    /* make really long so it covers the whole screen all the way to desktop */
    height: 250rem;
    background: #fff;
    opacity: 1;
    transform: rotate(-67deg);
    transform-origin: center;
    position: absolute;
    display: block;
    bottom: -139.6875rem;
    /* this makes the right edge sit at the 50% line at all times */
    right: 50%;
    z-index: 0;
  }
  #hero-143:after {
    /* Right side of the triangle */
    content: "";
    width: 31.25rem;
    height: 250rem;
    background: #fff;
    opacity: 1;
    transform: rotate(67deg);
    transform-origin: center;
    position: absolute;
    display: block;
    bottom: -139.6875rem;
    /* this makes the left edge sit at the 50% line at all times */
    left: 50%;
    z-index: 0;
  }
  #hero-143 .cs-background {
    width: 100%;
    height: 100%;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -2;
  }
  #hero-143 .cs-background:before {
    /* Overlay */
    content: "";
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.7;
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    z-index: 1;
    /* prevents the cursor from interacting with it */
    pointer-events: none;
  }
  #hero-143 .cs-background img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  #hero-143 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
  }
  #hero-143 .cs-title {
    /* 39px - 61px */
    font-size: clamp(2.4375rem, 6.4vw, 3.8125rem);
    font-weight: 700;
    line-height: 1.2em;
    text-align: center;
    max-width: 51.8125rem;
    /* 16px - 24px */
    margin: 0 auto clamp(1rem, 4vw, 1.5rem);
    color: #fff;
    position: relative;
  }
  #hero-143 .cs-title:after {
    /* Divider Line */
    content: "";
    /* 60px - 100px */
    width: clamp(3.75rem, 9.5vw, 6.25rem);
    /* 4px - 8px */
    height: clamp(0.25rem, 0.8vw, 0.5rem);
    /* 16px - 24px */
    margin: clamp(1rem, 4vw, 1.5rem) auto clamp(1rem, 4vw, 1.5rem);
    background: var(--primary);
    opacity: 1;
    position: relative;
    display: block;
  }
  #hero-143 .cs-text {
    /* 16px - 25px */
    font-size: clamp(1rem, 1.95vw, 1.5625rem);
    line-height: 1.5em;
    text-align: center;
    width: 100%;
    /* 464px - 800px */
    max-width: clamp(29rem, 60vw, 50rem);
    margin: 0 auto;
    /* 40px - 48px */
    margin-bottom: clamp(2.5rem, 4vw, 3rem);
    color: #fff;
  }
  #hero-143 .cs-button-solid {
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 700;
    margin: auto;
    color: #1a1a1a;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: color 0.3s;
  }
  #hero-143 .cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #000;
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
  }
  #hero-143 .cs-button-solid:hover {
    color: #fff;
  }
  #hero-143 .cs-button-solid:hover:before {
    width: 100%;
  }
}
/* Desktop - 1300px (To make image background parallax) */
@media only screen and (min-width: 81.25rem) {
  #hero-143 {
    background: url("https://csimg.nyc3.digitaloceanspaces.com/Hero/kitchen.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* makes the parallax effect */
    background-attachment: fixed;
  }
  #hero-143 .cs-background img {
    display: none;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #hero-143:before, body.dark-mode #hero-143:after {
    background: var(--dark);
  }
  body.dark-mode #hero-143 .cs-background:before {
    opacity: 0.85;
  }
}
/*-- -------------------------- -->
<---         Services           -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #h-services-143 {
    /* 40px - 100px */
    padding: 0 1rem clamp(2.5rem, 7.9vw, 6.25rem) 1rem;
    position: relative;
    /* give a higher z index than the hero so it can sit on top */
    z-index: 10;
  }
  #h-services-143 .cs-card-group {
    width: 100%;
    max-width: 29.0625rem;
    margin: 0 auto 0;
    /* negative margin pulls it up on top of the hero section */
    /* -46px to -76px - we're calculating whatever clamp is and multiplying by -1
    to make the value negative since clamp doesn't work with negative values */
    margin-top: calc(clamp(5rem, 13vw, 4.75rem) * -1);
    /* 40px - 60px top and bottom, 16px - 44px left & right */
    padding: clamp(2.5rem, 4.7vw, 3.75rem) clamp(1rem, 5.3vw, 2.75rem);
    box-shadow: 0px 20px 40px 0px rgba(0, 0, 0, 0.05);
    border-radius: 0.3125rem;
    border-top: 8px solid var(--primary);
    background-color: #fff;
    /* prevents padding and border from affecting height and width */
    box-sizing: border-box;
  }
  #h-services-143 .cs-item {
    list-style: none;
    margin: 0 auto 2rem;
    width: 100%;
    max-width: 22.5rem;
  }
  #h-services-143 .cs-item:last-of-type {
    margin-bottom: 0;
  }
  #h-services-143 .cs-icon {
    /* 68px - 88px */
    width: clamp(4.25rem, 8.8vw, 5.5rem);
    /* 68px - 88px */
    height: clamp(4.25rem, 8.8vw, 5.5rem);
    margin: auto;
    /* 20px - 24px */
    margin-bottom: clamp(1.25rem, 5vw, 1.5rem);
    background: var(--primary);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  #h-services-143 .cs-icon img {
    /* 44px - 52px */
    width: clamp(2.75rem, 5.8vw, 3.25rem);
    height: auto;
  }
  #h-services-143 .cs-title {
    font-size: 1.25rem;
    line-height: 1.2em;
    text-transform: uppercase;
    text-align: center;
    margin: 0 auto 0.5rem;
    color: var(--headerColor);
  }
  #h-services-143 .cs-text {
    font-size: 1rem;
    text-align: center;
    line-height: 1.5em;
    max-width: 22.5rem;
    margin: 0 auto;
    color: var(--bodyTextColor);
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #h-services-143 .cs-card-group {
    max-width: 49rem;
    /* -76px to -320px - we're calculating whatever clamp is and multiplying by -1
       to make the value negative since clamp doesn't work with negative values */
    margin-top: calc(clamp(4.75rem, 20vw, 20rem) * -1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    column-gap: 2.5rem;
  }
  #h-services-143 .cs-item {
    width: 45%;
    max-width: 19.625rem;
  }
  #h-services-143 .cs-item:last-of-type {
    margin-bottom: 2rem;
  }
}
/* Desktop - 1300px */
@media only screen and (min-width: 81.25rem) {
  #h-services-143 .cs-card-group {
    max-width: 80rem;
    flex-wrap: nowrap;
  }
  #h-services-143 .cs-item {
    margin: 0;
  }
  #h-services-143 .cs-item:last-of-type {
    margin-bottom: 0;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #h-services-143 .cs-card-group {
    background: var(--medium);
  }
  body.dark-mode #h-services-143 .cs-icon {
    background: var(--darkTint);
  }
  body.dark-mode #h-services-143 .cs-title {
    color: #fff;
  }
  body.dark-mode #h-services-143 .cs-text {
    color: #fff;
  }
}

/*# sourceMappingURL=critical.css.map */
