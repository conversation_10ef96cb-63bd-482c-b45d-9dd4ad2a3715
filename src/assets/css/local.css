/*-- -------------------------- -->
<---          Gallery           -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #gallery-43 {
    padding: var(--sectionPadding);
    position: relative;
    overflow: hidden;
  }
  #gallery-43 .cs-container {
    width: 100%;
    max-width: 69rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(3rem, 6vw, 4rem);
  }
  #gallery-43 .cs-content {
    text-align: center;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  #gallery-43 .cs-title {
    max-width: 63.75rem;
  }
  #gallery-43 .cs-text {
    max-width: 50.625rem;
  }
  #gallery-43 .cs-image-group {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: clamp(1rem, 1.5vw, 1.25rem);
  }
  #gallery-43 .cs-item {
    width: 100%;
    height: 100%;
    aspect-ratio: 1;
    margin: 0;
    position: relative;
    display: block;
  }
  #gallery-43 .cs-item:hover .cs-hover-box {
    opacity: 1;
  }
  #gallery-43 .cs-item:hover .cs-icon {
    transform: rotateY(0);
  }
  #gallery-43 .cs-item:hover .cs-h3,
  #gallery-43 .cs-item:hover .cs-hover-box-text {
    opacity: 1;
    transform: translateY(0);
  }
  #gallery-43 .cs-picture {
    margin: auto;
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
  }
  #gallery-43 .cs-picture img {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  #gallery-43 .cs-hover-box {
    text-align: center;
    width: 100%;
    height: 100%;
    padding: 1em;
    background-color: rgba(79, 139, 186, 0.9);
    opacity: 0;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    perspective: 700px;
    top: 0;
    left: 0;
    z-index: 1;
    pointer-events: none;
    transition: opacity 0.3s;
  }
  #gallery-43 .cs-icon {
    width: clamp(3.125rem, 5vw, 3.75rem);
    height: clamp(3.125rem, 5vw, 3.75rem);
    margin-bottom: clamp(1rem, 5vw, 2rem);
    border-radius: 50%;
    background-color: #fff;
    outline: 0.5rem solid rgba(255, 255, 255, 0.7);
    transform: rotateY(90deg);
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex: none;
    transition: transform 0.5s;
  }
  #gallery-43 .cs-icon img {
    width: 1.25rem;
    height: 1.25rem;
  }
  #gallery-43 .cs-h3 {
    font-size: clamp(1.25rem, 2vw, 1.5625rem);
    line-height: 1.2em;
    font-weight: 700;
    margin: 0 auto;
    max-width: 16.875rem;
    color: #fff;
    opacity: 0;
    transform: translateY(0.625rem);
    margin-bottom: 0.5rem;
    transition: opacity 0.3s, transform 0.3s ease-out;
    transition-delay: 0.1s;
  }
  #gallery-43 .cs-hover-box-text {
    font-size: clamp(0.8125rem, 1vw, 1rem);
    line-height: 1.5em;
    margin: 0 auto;
    max-width: 16.875rem;
    color: #fff;
    opacity: 0;
    transform: translateY(0.625rem);
    transition: opacity 0.3s, transform 0.3s ease-out;
    transition-delay: 0.2s;
  }
}
/* In Between - 600px */
@media only screen and (min-width: 37.5rem) {
  #gallery-43 .cs-image-group {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: 1fr;
  }
  #gallery-43 .cs-item {
    grid-column: span 6;
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #gallery-43 .cs-item {
    grid-column: span 4;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #gallery-43 .cs-title,
  body.dark-mode #gallery-43 .cs-text {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #gallery-43 .cs-text {
    opacity: 0.8;
  }
}
/*-- -------------------------- -->
<---   Side By Side Reverse     -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #sbsr-2289 {
    padding: var(--sectionPadding);
    overflow: hidden;
    background-color: #f8fafc;
  }
  #sbsr-2289 .cs-container {
    width: 100%;
    /* changes to 1280px at desktop */
    max-width: 36.5rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 3rem;
  }
  #sbsr-2289 .cs-content {
    /* set text align to left if content needs to be left aligned */
    text-align: left;
    width: 100%;
    display: flex;
    flex-direction: column;
    /* centers content horizontally, set to flex-start to left align */
    align-items: flex-start;
    position: relative;
    z-index: 10;
  }
  #sbsr-2289 .cs-text {
    margin-bottom: 1rem;
  }
  #sbsr-2289 .cs-text:last-of-type {
    margin-bottom: 2rem;
  }
  #sbsr-2289 .cs-features {
    width: 100%;
    margin: 0 0 2rem;
    /* 24px - 32px */
    padding: clamp(1.5rem, 4vw, 2rem);
    background-color: #fff;
    border-left: 0.25rem solid var(--secondary);
  }
  #sbsr-2289 .cs-h3 {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.5em;
    margin: 0 0 0.75rem;
    color: var(--headerColor);
  }
  #sbsr-2289 .cs-ul {
    font-size: 0.75rem;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  #sbsr-2289 .cs-li {
    font-size: 1rem;
    line-height: 1.5em;
    list-style: none;
    /* 20px - 24px */
    padding-left: clamp(1.25rem, 3vw, 1.5rem);
    color: var(--bodyTextColor);
    position: relative;
  }
  #sbsr-2289 .cs-li::before {
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    margin-top: 0.5rem;
    background-color: var(--secondary);
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(45deg);
  }
  #sbsr-2289 .cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-align: center;
    text-decoration: none;
    min-width: 9.375rem;
    margin: 0;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    padding: 0 1.5rem;
    background-color: var(--primary);
    color: #1a1a1a;
    display: inline-block;
    position: relative;
    z-index: 1;
    transition: color 0.3s;
  }
  #sbsr-2289 .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #000;
    opacity: 1;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #sbsr-2289 .cs-button-solid:hover {
    color: #fff;
  }
  #sbsr-2289 .cs-button-solid:hover:before {
    width: 100%;
  }
  #sbsr-2289 .cs-image-group {
    width: 100%;
    height: 126vw;
    max-height: 39.125rem;
    display: block;
    order: -1;
    position: relative;
    z-index: 1;
  }
  #sbsr-2289 .cs-picture {
    /* big background image */
    width: 100%;
    display: block;
    position: absolute;
    top: 0;
    bottom: 5.9375rem;
    left: 0;
  }
  #sbsr-2289 .cs-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
  #sbsr-2289 .cs-box {
    text-align: left;
    width: 91%;
    max-width: 28.125rem;
    /* prevents padding and border from affecting height and width */
    box-sizing: border-box;
    /* 24px - 32px */
    padding: clamp(1.5rem, 3vw, 2rem);
    background-color: #fffcf3;
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    column-gap: 1.5rem;
    row-gap: 0.5rem;
    position: absolute;
    right: 50%;
    bottom: 0;
    z-index: 10;
    transform: translate(50%);
  }
  #sbsr-2289 .cs-number {
    /* 31px - 49px */
    font-size: clamp(1.9375rem, 4vw, 3.0625rem);
    font-weight: 700;
    line-height: 1.2em;
    text-transform: uppercase;
    margin: 0;
    color: var(--General-Secondary, #ffc219);
  }
  #sbsr-2289 .cs-heading {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.2em;
    margin: 0 0 0.5rem;
    color: var(--headerColor);
  }
  #sbsr-2289 .cs-desc {
    font-size: 1rem;
    line-height: 1.5em;
    color: var(--bodyTextColor);
  }
  #sbsr-2289 .cs-graphic {
    width: 150%;
    height: auto;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: -1;
    transform: translate(-50%, -50%);
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #sbsr-2289 .cs-box {
    flex-direction: row;
    align-items: center;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #sbsr-2289 .cs-container {
    max-width: 80rem;
    flex-direction: row;
    align-items: stretch;
    gap: 1.25rem;
  }
  #sbsr-2289 .cs-content {
    padding-right: 3.25rem;
    padding-bottom: 6.75rem;
  }
  #sbsr-2289 .cs-image-group {
    height: auto;
    min-height: 40.125rem;
    max-height: 100%;
    order: initial;
  }
  #sbsr-2289 .cs-picture {
    bottom: 4rem;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #sbsr-2289 {
    background-color: var(--darkModeBackground);
  }
  body.dark-mode #sbsr-2289 .cs-title,
  body.dark-mode #sbsr-2289 .cs-text,
  body.dark-mode #sbsr-2289 .cs-h3,
  body.dark-mode #sbsr-2289 .cs-li,
  body.dark-mode #sbsr-2289 .cs-heading,
  body.dark-mode #sbsr-2289 .cs-desc {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #sbsr-2289 .cs-text,
  body.dark-mode #sbsr-2289 .cs-li,
  body.dark-mode #sbsr-2289 .cs-desc {
    opacity: 0.8;
  }
  body.dark-mode #sbsr-2289 .cs-features,
  body.dark-mode #sbsr-2289 .cs-box {
    background-color: var(--medium);
  }
}
/*-- -------------------------- -->
<---          GALLERY           -->
<--- -------------------------- -*/
@media only screen and (min-width: 0rem) {
  #gallery-2297 {
    padding: var(--sectionPadding);
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
  #gallery-2297 .cs-container {
    width: 100%;
    max-width: 80rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    z-index: 1;
  }
  #gallery-2297 .cs-content {
    width: 100%;
    display: contents;
  }
  #gallery-2297 .cs-title {
    margin: 0 0 3rem;
  }
  #gallery-2297 .cs-text {
    max-width: 50.625rem;
  }
  #gallery-2297 .cs-button-group {
    margin: 2rem auto 0;
    display: flex;
    order: 2;
    column-gap: clamp(1.25rem, 3.4vw, 2.5rem);
    row-gap: 1rem;
  }
  #gallery-2297 .cs-button {
    padding: 1rem;
    background-color: #f3f3f3;
    border: none;
    transition: background-color 0.3s;
  }
  #gallery-2297 .cs-button:hover {
    background-color: var(--primary);
    cursor: pointer;
  }
  #gallery-2297 .cs-button:hover .cs-arrow {
    filter: invert(1) brightness(1000%);
  }
  #gallery-2297 .cs-arrow {
    width: 1.25rem;
    height: 1.25rem;
    display: block;
  }
  #gallery-2297 .cs-gallery-wrapper {
    width: 100%;
    position: relative;
    z-index: 1;
  }
  #gallery-2297 .cs-gallery {
    width: 100%;
    margin: 0;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: clamp(1rem, 1.5vw, 1.25rem);
    position: relative;
    transform-style: preserve-3d;
    perspective: 700px;
    transition: transform 0.7s, opacity 0.3s, visibility 0.5s, top 0.3s, left 0.3s;
    transform-origin: left top;
  }
  #gallery-2297 .cs-gallery.cs-hidden {
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    transform: scaleY(0) scaleX(0);
  }
  #gallery-2297 .cs-gallery.cs-hidden .cs-image {
    opacity: 0;
    transform: translateY(2.1875rem) rotateX(90deg);
  }
  #gallery-2297 .cs-image {
    min-height: clamp(20rem, 20vw, 32.75rem);
    overflow: hidden;
    opacity: 1;
    display: block;
    grid-column: span 12;
    position: relative;
    transform: translateY(0rem) rotateX(0);
    transition: opacity 0.6s, transform 0.6s;
  }
  #gallery-2297 .cs-image:nth-of-type(1) {
    transition-delay: 0.1s;
  }
  #gallery-2297 .cs-image:nth-of-type(2) {
    transition-delay: 0.2s;
  }
  #gallery-2297 .cs-image:nth-of-type(3) {
    transition-delay: 0.3s;
  }
  #gallery-2297 .cs-picture {
    width: 100%;
    height: 100%;
    background-color: #000;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
  #gallery-2297 .cs-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    transition: transform 0.65s, opacity 0.3s;
  }
  #gallery-2297 .cs-info {
    width: 90%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 10;
  }
  #gallery-2297 .cs-tag {
    font-size: clamp(0.8125rem, 2vw, 1rem);
    font-weight: 700;
    text-transform: uppercase;
    padding: 0.5rem 1.25rem;
    background-color: var(--primary);
    color: var(--bodyTextColorWhite);
  }
  #gallery-2297 .cs-project {
    font-size: 1.25rem;
    font-weight: 700;
    text-transform: uppercase;
    padding: 0.75rem 1rem;
    background-color: #fff;
    color: var(--headerColor);
  }
  #gallery-2297 .cs-stats {
    max-width: 80rem;
    margin: clamp(3rem, 9vw, 3.5rem) auto 0;
    display: flex;
    flex-direction: column;
    column-gap: clamp(1rem, 3vw, 1.5rem);
    row-gap: 2rem;
  }
  #gallery-2297 .cs-stat {
    display: flex;
    flex-direction: column;
    column-gap: 1.25rem;
    row-gap: 0.5rem;
  }
  #gallery-2297 .cs-number {
    font-size: var(--headerFontSize);
    font-weight: 700;
    line-height: 1.2em;
    color: var(--primary);
  }
  #gallery-2297 .cs-desc {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1.2em;
    color: var(--headerColor);
  }
}
@media only screen and (min-width: 48rem) {
  #gallery-2297 .cs-container {
    gap: clamp(3rem, 6vw, 4rem);
  }
  #gallery-2297 .cs-content {
    text-align: left;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 1.5rem;
  }
  #gallery-2297 .cs-title {
    margin: 0;
  }
  #gallery-2297 .cs-button-group {
    margin: 0;
  }
  #gallery-2297 .cs-image {
    grid-column: span 4;
  }
  #gallery-2297 .cs-stats {
    flex-direction: row;
  }
  #gallery-2297 .cs-stat {
    flex: 1;
  }
}
@media only screen and (min-width: 64rem) {
  #gallery-2297 .cs-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    gap: 4rem;
  }
  #gallery-2297 .cs-image:hover .cs-tag,
  #gallery-2297 .cs-image:hover .cs-project {
    opacity: 1;
    transform: translateX(0);
  }
  #gallery-2297 .cs-image:hover .cs-picture img {
    opacity: 0.2;
    transform: scale(1.1);
  }
  #gallery-2297 .cs-tag {
    opacity: 0;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out, opacity 0.3s;
  }
  #gallery-2297 .cs-project {
    opacity: 0;
    transform: translateX(-100%);
    transition: transform 0.4s ease-out, opacity 0.3s;
    transition-delay: 0.1s;
  }
}
@media only screen and (min-width: 81.25rem) {
  #gallery-2297 .cs-stat {
    flex-direction: row;
    align-items: center;
  }
}
/*-- -------------------------- -->
<---        Side By Side        -->
<--- -------------------------- -*/
/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
  #sbs-2295 {
    padding: var(--sectionPadding);
    background-color: #f8fafc;
    overflow: hidden;
    position: relative;
    z-index: 1;
  }
  #sbs-2295 .cs-container {
    width: 100%;
    /* changes to 1280px at tablet */
    max-width: 44rem;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* 48px - 64px */
    gap: clamp(3rem, 4vw, 4rem);
  }
  #sbs-2295 .cs-image-group {
    /* scales the whole section down and ties the font size to the vw and stops at 75% of the vale of 1em, changes at desktop */
    font-size: min(1.98vw, 0.826rem);
    /* everything inside this box is in ems so we can scale it all down proportionally with a font size */
    width: 46.125em;
    height: 36.125em;
    order: -1;
    position: relative;
    z-index: 1;
    /* prevents flexbox from squishing it */
    flex: none;
  }
  #sbs-2295 .cs-picture {
    width: 100%;
    height: 100%;
    border: 1em solid var(--bodyTextColorWhite);
    display: block;
  }
  #sbs-2295 .cs-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    top: 0;
    left: 0;
  }
  #sbs-2295 .cs-picture-wrapper1 {
    width: 25.75em;
    height: 28.5625em;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    position: absolute;
    top: 0;
    left: 0;
  }
  #sbs-2295 .cs-picture-wrapper2 {
    width: 25.8125em;
    height: 29.625em;
    display: flex;
    flex-direction: column-reverse;
    gap: 1rem;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  #sbs-2295 .cs-tag {
    /* 14px - 24px */
    font-size: clamp(0.875rem, 1.4vw, 1.5rem);
    font-weight: 700;
    line-height: 1.2em;
    text-align: center;
    color: var(--headerColor);
  }
  #sbs-2295 .cs-arrow {
    width: 9.25em;
    height: 3.375em;
    position: absolute;
    top: 15.125em;
    left: 15.9375em;
    transform: rotate(30deg);
  }
  #sbs-2295 .cs-content {
    /* set text align to center if content needs to be centrally aligned */
    text-align: left;
    width: 100%;
    max-width: 36.625rem;
    display: flex;
    flex-direction: column;
    /* aligns content to the left, set to center to centrally align */
    align-items: flex-start;
  }
  #sbs-2295 .cs-title {
    max-width: 30ch;
  }
  #sbs-2295 .cs-text {
    margin-bottom: 1rem;
  }
  #sbs-2295 .cs-text:last-of-type {
    margin-bottom: 1.5rem;
  }
  #sbs-2295 .cs-button-solid {
    font-size: 1rem;
    font-weight: 700;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-align: center;
    text-decoration: none;
    min-width: 9.375rem;
    margin: 0;
    padding: 0 1.5rem;
    background-color: var(--primary);
    color: var(--headerColor);
    display: inline-block;
    position: relative;
    z-index: 1;
  }
  #sbs-2295 .cs-button-solid:before {
    content: "";
    width: 0%;
    height: 100%;
    background: #000;
    opacity: 1;
    border-radius: 0.25rem;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    transition: width 0.3s;
  }
  #sbs-2295 .cs-button-solid:hover:before {
    width: 100%;
  }
  #sbs-2295 .cs-h3 {
    font-size: 1rem;
    font-weight: 700;
    line-height: 1.5em;
    margin: 0;
    color: var(--headerColor);
  }
  #sbs-2295 .cs-ul {
    margin: 0.75rem 0 2rem;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  #sbs-2295 .cs-li {
    font-size: 1rem;
    line-height: 1.5em;
    list-style: none;
    /* 20px - 24px */
    padding-left: clamp(1.25rem, 3vw, 1.5rem);
    color: var(--bodyTextColor);
    position: relative;
  }
  #sbs-2295 .cs-li strong {
    color: #1a1a1a;
  }
  #sbs-2295 .cs-li::before {
    content: "";
    width: 0.5rem;
    height: 0.5rem;
    margin-top: 0.5rem;
    background-color: var(--secondary);
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    transform: rotate(45deg);
  }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
  #sbs-2295 .cs-container {
    max-width: 80rem;
  }
  #sbs-2295 .cs-image-group {
    order: initial;
  }
}
/* Desktop - 1024px */
@media only screen and (min-width: 64rem) {
  #sbs-2295 .cs-container {
    flex-direction: row;
    align-items: stretch;
  }
  #sbs-2295 .cs-image-group {
    font-size: min(1.03vw, 1rem);
    height: auto;
  }
  #sbs-2295 .cs-picture-wrapper1 {
    height: auto;
    bottom: 7.5625em;
  }
  #sbs-2295 .cs-picture-wrapper2 {
    height: auto;
    top: 6.5em;
  }
}
/* Dark Mode */
@media only screen and (min-width: 0rem) {
  body.dark-mode #sbs-2295 {
    background-color: rgba(0, 0, 0, 0.2);
  }
  body.dark-mode #sbs-2295 .cs-title,
  body.dark-mode #sbs-2295 .cs-text,
  body.dark-mode #sbs-2295 .cs-h3,
  body.dark-mode #sbs-2295 .cs-li,
  body.dark-mode #sbs-2295 .cs-tag {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #sbs-2295 .cs-text,
  body.dark-mode #sbs-2295 .cs-li {
    opacity: 0.8;
  }
  body.dark-mode #sbs-2295 .cs-text strong,
  body.dark-mode #sbs-2295 .cs-li strong {
    color: var(--bodyTextColorWhite);
  }
  body.dark-mode #sbs-2295 .cs-picture {
    border-color: var(--medium);
  }
}

/*# sourceMappingURL=local.css.map */
