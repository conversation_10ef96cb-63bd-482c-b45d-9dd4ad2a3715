---
title: "Pixel Perfect Websites | Code Stitch Web Designs | Denver, CO"
description: "Meta description for the page"
preloadImg: "/assets/images/landing.jpg"
permalink: "/"
tags: "sitemap" # Sitemap will use all pages in content/, due to content.json. As index.html is not included in content/ we need to manually add the sitemap tag to this page (only).
eleventyNavigation:
    key: Home
    order: 100
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/local.css" />
    <link rel="stylesheet" href="/assets/css/critical.css" />

    <!-- Script for Netlify Identity -->
    <script
        defer
        src="https://identity.netlify.com/v1/netlify-identity-widget.js"
    ></script>
    <script defer>
        if (window.netlifyIdentity) {
            window.netlifyIdentity.on("init", (user) => {
                if (!user) {
                    window.netlifyIdentity.on("login", () => {
                        document.location.href = "/admin/";
                    });
                }
            });
        }
    </script>

    <!-- JSON Schema Markup -->
    <!-- prettier-ignore-start -->
    <script type="application/ld+json">
        {
            "@context": "http://schema.org",
            "@type": "LocalBusiness",
            "name": "{{ client.name }}",
            {% if preloadImg %}"image": "{{ preloadImg }}",{% endif %}
            {% if client.phoneFormatted %}"telephone": "{{ client.phoneFormatted }}",{% endif %}
            {% if client.email %}"email": "{{ client.email }}",{% endif %}
            {% if client.address %}
                "address": {
                    "@type": "PostalAddress"{% if client.address.lineOne %},
                        "streetAddress": "{{ client.address.lineOne }}{% if client.address.lineTwo %}, {{ client.address.lineTwo }}{% endif %}"
                    {% endif %}
                    {% if client.address.city %},
                        "addressLocality": "{{ client.address.city }}"
                    {% endif %}
                    {% if client.address.state %},
                        "addressRegion": "{{ client.address.state }}"
                    {% endif %}
                    {% if client.address.zip %},
                        "postalCode": "{{ client.address.zip }}"
                    {% endif %}
                    {% if client.address.country %},
                        "addressCountry": "{{ client.address.country }}"
                    {% endif %}
                },
            {% endif %}
            {% if client.domain and page.url %}"url": "{{ client.domain }}{{ page.url }}",{% endif %}
            {% if client.socials %}
                "sameAs": [{% for platform, url in client.socials %}
                        {% if not loop.first %},{% endif %}
                        "{{ url }}"
                    {% endfor %}]
            {% endif %}
        }
    </script>
    <!-- prettier-ignore-end -->
{% endblock %}

{% block body %}
    <!--We needed to pull from Google Fonts CDN to load in the cursive font. -->
    <!--Download the fonts from https://gwfh.mranftl.com/fonts and follow the steps to host it locally and remove these CDN links-->

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Mr+Dafoe&display=swap"
        rel="stylesheet"
    />

    <!-- ============================================ -->
    <!--                    Hero                      -->
    <!-- ============================================ -->

    <section id="hero-143">
        <div class="cs-container">
            <h1 class="cs-title">[Your Brand Name]</h1>
            <p class="cs-text">
                [A compelling description of your business and what makes you
                unique. Highlight your main value proposition in 1-2 sentences.]
            </p>
            <a href="" class="cs-button-solid">Call to Action</a>
        </div>

        <!-- Background Image -->
        <picture class="cs-background">
            <source
                media="(max-width: 600px)"
                srcset="
                    https://csimg.nyc3.digitaloceanspaces.com/Hero/kitchen-m.jpg
                "
            />
            <source
                media="(min-width: 601px)"
                srcset="
                    https://csimg.nyc3.digitaloceanspaces.com/Hero/kitchen.jpg
                "
            />
            <img
                loading="lazy"
                decoding="async"
                src="https://csimg.nyc3.digitaloceanspaces.com/Hero/kitchen.jpg"
                alt="kitchen"
                width="2250"
                height="1500"
                aria-hidden="true"
            />
        </picture>
    </section>

    <!-- ============================================ -->
    <!--                   Services                   -->
    <!-- ============================================ -->

    <section id="h-services-143">
        <ul class="cs-card-group">
            <li class="cs-item">
                <picture class="cs-icon">
                    <img
                        loading="lazy"
                        decoding="async"
                        src="https://csimg.nyc3.digitaloceanspaces.com/Hero/toolbox.svg"
                        alt="icon"
                        width="52"
                        height="52"
                        aria-hidden="true"
                    />
                </picture>
                <h2 class="cs-title">Service 1</h2>
                <p class="cs-text">
                    [Brief description of your first main service offering]
                </p>
            </li>
            <li class="cs-item">
                <picture class="cs-icon">
                    <img
                        loading="lazy"
                        decoding="async"
                        src="https://csimg.nyc3.digitaloceanspaces.com/Hero/buildings.svg"
                        alt="icon"
                        width="52"
                        height="52"
                        aria-hidden="true"
                    />
                </picture>
                <h2 class="cs-title">Service 2</h2>
                <p class="cs-text">
                    [Brief description of your second main service offering]
                </p>
            </li>
            <li class="cs-item">
                <picture class="cs-icon">
                    <img
                        loading="lazy"
                        decoding="async"
                        src="https://csimg.nyc3.digitaloceanspaces.com/Hero/gear.svg"
                        alt="icon"
                        width="52"
                        height="52"
                        aria-hidden="true"
                    />
                </picture>
                <h2 class="cs-title">Service 3</h2>
                <p class="cs-text">
                    [Brief description of your third main service offering]
                </p>
            </li>
            <li class="cs-item">
                <picture class="cs-icon">
                    <img
                        loading="lazy"
                        decoding="async"
                        src="https://csimg.nyc3.digitaloceanspaces.com/Hero/user.svg"
                        alt="icon"
                        width="52"
                        height="52"
                        aria-hidden="true"
                    />
                </picture>
                <h2 class="cs-title">Service 4</h2>
                <p class="cs-text">
                    [Brief description of your fourth main service offering]
                </p>
            </li>
        </ul>
    </section>

    <!-- ============================================ -->
    <!--                   Gallery                    -->
    <!-- ============================================ -->

    <section id="gallery-43">
        <div class="cs-container">
            <div class="cs-content">
                <span class="cs-topper">Featured Floor Plans</span>
                <h2 class="cs-title">
                    Our Most Popular and Customizable Floor Plans
                </h2>
                <p class="cs-text">
                    Every family is different — your home should be too. At
                    Donovan Builders LLC, we offer a wide selection of
                    thoughtfully designed floor plans to match your lifestyle,
                    space needs, and aesthetic preferences. Each plan is fully
                    customizable, or we can bring your existing plans to life
                    with expert craftsmanship. Browse our featured layouts to
                    find the perfect starting point for your dream home.
                </p>
            </div>
            <div class="cs-image-group">
                <a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/3990359/pexels-photo-3990359.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Caitlyn</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor, sit amet consectetur adipisicing elit. Dignissimos tempore praesentium, nobis voluptas architecto nisi. Odio corporis totam tempora a.
                        </p>
                    </div></a
                ><a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/735319/pexels-photo-735319.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Ashleigh</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor sit, amet consectetur adipisicing elit. Accusamus, a sit tempora commodi quia libero similique optio necessitatibus harum omnis.
                        </p>
                    </div></a
                ><a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/3562689/pexels-photo-3562689.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Luke</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quod.
                        </p>
                    </div></a
                ><a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/6473973/pexels-photo-6473973.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Michelle</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quod.
                        </p>
                    </div></a
                ><a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/6473977/pexels-photo-6473977.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Preston B</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quod.
                        </p>
                    </div></a
                ><a
                    class="cs-item"
                    href="/portfolio"
                    data-lightbox="floorplans"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt=""
                            height="346"
                            src="https://images.pexels.com/photos/6474493/pexels-photo-6474493.jpeg"
                            width="346"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-hover-box">
                        <picture class="cs-icon"
                            ><img
                                alt=""
                                height="20"
                                src="/assets/svgs/arrow-right.svg"
                                width="20"
                        /></picture>
                        <h3 class="cs-h3">Sarah</h3>
                        <p class="cs-hover-box-text">
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quod.
                        </p>
                    </div></a
                >
            </div>
            <a class="cs-button-solid" href="/portfolio"
                >View All Floor Plans</a
            >
        </div>
    </section>

<!-- ============================================ -->
<!--             Side By Side Reverse             -->
<!-- ============================================ -->

<section id="sbsr-2289">
    <div class="cs-container">
        <!-- Left Content Section-->
        <div class="cs-content">
            <span class="cs-topper">Featured service</span>
            <h2 class="cs-title">Creating Elegant Outdoor Living Spaces</h2>
            <p class="cs-text">
                Transform your outdoor spaces with our hardscaping services. We design stunning patios, walkways, retaining walls, and outdoor kitchens, combining creativity and expertise to enhance your relaxation and entertainment areas.
            </p>
            <div class="cs-features">
                <h3 class="cs-h3">Key Features:</h3>
                <ul class="cs-ul">
                    <li class="cs-li">Custom design tailored to your outdoor space</li>
                    <li class="cs-li">Durable and aesthetically pleasing materials</li>
                    <li class="cs-li">Seamless integration with your home’s architecture</li>
                </ul>
            </div>
            <a class="cs-button-solid" aria-label="learn more about our programs" href="">Read More</a>
        </div>
        <!-- Right Image Section -->
        <div class="cs-image-group">
            <!--Floating Box-->
            <div class="cs-box">
                <span class="cs-number">12K+</span>
                <div class="cs-flex">
                    <h3 class="cs-heading">Square Meters Paved</h3>
                    <span class="cs-desc">Hardscaping projects that combine beauty and functionality.</span>
                </div>
            </div>
            <!--Image-->
            <picture class="cs-picture">
                <source media="(max-width: 600px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/People/construction3.jpg">
                <source media="(min-width: 601px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/People/construction3.jpg">
                <img loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/People/construction3.jpg" alt="meeting" width="630" height="630" aria-hidden="true">
            </picture>
        </div>
    </div>
</section>
                                

<!-- ============================================ -->
<!--                 Recommended                  -->
<!-- ============================================ -->

<section id="gallery-2297">
    <div class="cs-container">
        <div class="cs-content">
            <div class="cs-flex">
                <span class="cs-topper">Recently Added</span>
                <h2 class="cs-title">Recent Additions to our Directory</h2>
                <p class="cs-text">
                    Explore the latest additions to our directory, where
                    you'll find a diverse range of accounting professionals
                    across the United States.
                </p>
            </div>
        </div>
        <div class="cs-gallery-wrapper">
            <div class="cs-gallery">
                <a class="cs-image" href="/all-in-accounting-solutions-llc/"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt="gallery"
                            height="400"
                            src="https://lh3.googleusercontent.com/p/AF1QipOqBgm2XvQXAbTE1SuwAL92HymCtYRc3682dOqo=w408-h326-k-no"
                            width="305"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-info">
                        <span class="cs-project">All In Accounting</span>
                        <span class="cs-tag">Lebanon, OR</span>
                    </div></a
                ><a class="cs-image" href="/mile-high-cpas/"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt="gallery"
                            height="400"
                            src="https://images.squarespace-cdn.com/content/v1/5e299f123bd0b35f812ee3fb/*************-P1S6HYBJML9H2LF66C8Q/MHCPA-Logo.png?format=750w"
                            width="305"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-info">
                        <span class="cs-project">Mile High CPAs</span>
                        <span class="cs-tag">Denver, CO</span>
                    </div></a
                ><a class="cs-image" href="/arthasmart-tax-solutions/"
                    ><picture class="cs-picture"
                        >
                        <img
                            alt="gallery"
                            height="400"
                            src="https://images.squarespace-cdn.com/content/v1/674f790113fb766439995e6b/eba72bec-a8ae-40d2-a69b-d1a9f3cf0d3e/AS-Logo-Horizontal.png?format=1500w"
                            width="305"
                            decoding="async"
                            loading="lazy"
                    /></picture>
                    <div class="cs-info">
                        <span class="cs-project"
                            >Arthasmart Tax Solutions</span
                        >
                        <span class="cs-tag">Issaquah, WA</span>
                    </div></a
                >
            </div>
        </div>
    </div>
    <div class="cs-stats">
        <div class="cs-stat">
            <span class="cs-number">3</span>
            <span class="cs-desc">In Demand Accountants</span>
        </div>
        <div class="cs-stat">
            <span class="cs-number">20+</span>
            <span class="cs-desc">Services Offered</span>
        </div>
        <div class="cs-stat">
            <span class="cs-number">25+</span>
            <span class="cs-desc">Years Experience</span>
        </div>
        <div class="cs-stat">
            <span class="cs-number">100%</span>
            <span class="cs-desc">Locally Rooted</span>
        </div>
    </div>
</section>

<!-- ============================================ -->
<!--                  Side By Side                -->
<!-- ============================================ -->

<section id="sbs-2295">
    <div class="cs-container">
        <div class="cs-image-group">
            <!--Main Image-->
            <div class="cs-picture-wrapper1">
                <picture class="cs-picture cs-picture1">
                    <!--Mobile Image-->
                    <source media="(max-width: 600px)" srcset="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-before1.jpg">
                    <!--Tablet and above Image-->
                    <source media="(min-width: 601px)" srcset="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-before1.jpg">
                    <img loading="lazy" decoding="async" src="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-before1.jpg" alt="construction" width="272" height="337">
                </picture>
                <span class="cs-tag">Before</span>
            </div>
            <!--Small Image-->
            <div class="cs-picture-wrapper2">
                <picture class="cs-picture cs-picture2">
                    <!--Mobile Image-->
                    <source media="(max-width: 600px)" srcset="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-after1.jpg">
                    <!--Tablet and above Image-->
                    <source media="(min-width: 601px)" srcset="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-after1.jpg">
                    <img loading="lazy" decoding="async" src="https://csimages2.nyc3.digitaloceanspaces.com/Images/Landscapes/construction-after1.jpg" alt="construction" width="243" height="367">
                </picture>
                <span class="cs-tag">After</span>
            </div>
            <img class="cs-arrow" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/before-after-arrow.svg" alt="arrow" width="148" height="54">
        </div>
        <div class="cs-content">
            <span class="cs-topper">Service</span>
            <h2 class="cs-title">Modern Home Extension</h2>
            <p class="cs-text">
                A seamless extension to an existing family home, adding a new living area and additional bedrooms. The project emphasized maintaining architectural consistency and enhancing the home's functionality.
            </p>
            <h3 class="cs-h3">Our Process</h3>
            <ul class="cs-ul">
                <li class="cs-li">
                    <strong>Initial Consultation</strong>: Identified the client's needs and their goals for future growth.
                </li>
                <li class="cs-li">
                    <strong>Design Integration</strong>: Ensured the extension matched the original structure.
                </li>
                <li class="cs-li">
                    <strong>Construction</strong>: Built the extension with high-quality materials.
                </li>
                <li class="cs-li">
                    <strong>Interior Finishing</strong>: We integrated the new space with the existing interiors, enhancing functionality and aesthetics.
                </li>
            </ul>
            <a href="" class="cs-button-solid">Book An Appointment</a>
        </div>
    </div>
</section>
                                

{% endblock %}
