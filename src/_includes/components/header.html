<!-- ============================================ -->
<!--                 Navigation                   -->
<!-- ============================================ -->

<header id="cs-navigation">
    <div class="cs-top-bar">
        <div class="cs-top-container">
            <div class="cs-top-contact">
                <a href="" class="cs-top-link">
                    <img class="cs-link-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/phone-grey.svg" alt="logo" width="16" height="16" aria-hidden="true" decoding="async">
                    [Your Phone Number]
                </a>
                <a href="" class="cs-top-link">
                    <img class="cs-link-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/clock-grey.svg" alt="logo" width="20" height="20" aria-hidden="true" decoding="async">
                    [Your Business Hours]
                </a>
            </div>
            <div class="cs-top-social">
                <a href="[Your Facebook URL]" class="cs-social-link">
                    <img class="cs-social-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/face-grey.svg" alt="facebook" width="12" height="12" aria-hidden="true" decoding="async">
                </a>
                <a href="[Your Twitter URL]" class="cs-social-link">
                    <img class="cs-social-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/twit-grey.svg" alt="twitter" width="12" height="12" aria-hidden="true" decoding="async">
                </a>
                <a href="[Your Instagram URL]" class="cs-social-link">
                    <img class="cs-social-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Icons/insta-grey.svg" alt="instagram" width="12" height="12" aria-hidden="true" decoding="async">
                </a>
            </div>
        </div>
    </div>
    <div class="cs-container">
        <!--Nav Logo-->
        <a href="/" class="cs-logo" aria-label="back to home">
            <img src="/assets/images/logo.png" alt="[Your Brand Name] logo" width="197" height="43" aria-hidden="true" decoding="async">
        </a>
        <!--Navigation List-->
        <nav class="cs-nav" role="navigation">
            <!--Mobile Nav Toggle-->
            <button class="cs-toggle" aria-label="mobile menu toggle">
                <div class="cs-box" aria-hidden="true">
                    <span class="cs-line cs-line1" aria-hidden="true"></span>
                    <span class="cs-line cs-line2" aria-hidden="true"></span>
                    <span class="cs-line cs-line3" aria-hidden="true"></span>
                </div>
            </button>
            <div class="cs-ul-wrapper">
                <ul id="cs-expanded" class="cs-ul">
                    {% set navPages = collections.all | eleventyNavigation %}

                    {# Loop through all pages with a eleventyNavigation in the frontmatter #}
                    {% for entry in navPages %}

                        {# Define a hasChild variable to make it easier to test what navigation items are have child dropdown pages #}
                        {% set hasChild = entry.children.length %}

                        {# Check the frontmatter for hideOnMobile/hideOnDesktop. Form a list of classes to be joined when the item is rendered #}
                        {% set hideClasses = [] %}
                        {% if entry.hideOnMobile %}
                            {% set hideClasses = (hideClasses.push("cs-hide-on-mobile"), hideClasses) %}
                        {% endif %}
                        {% if entry.hideOnDesktop %}
                            {% set hideClasses = (hideClasses.push("cs-hide-on-desktop"), hideClasses) %}
                        {% endif %}

                        {# If this page is a dropdown, give it the appropriate classes, icons and accessibility attributes #}
                        <li
                            class="cs-li {% if hasChild %}cs-dropdown{% endif %} {{ hideClasses | join(" ") }}"
                        >
                            {# If the page has child dropdown pages, render it as a <button> tag with the appropriate dropdown HTML #}
                            {% if hasChild %}

                                {# Check to see if the user's current page is one of the child pages. If so, apply the cs-active class to the dropdown parent #}
                                {% set activeClass = "" %}
                                {% for child in entry.children %}
                                    {% if child.url == page.url %}
                                        {% set activeClass = "cs-active" %}
                                    {% endif %}
                                {% endfor %}

                                {# Render the <button> with the active class, dropdown icon and child links #}
                                <button
                                    class="cs-li-link cs-dropdown-button {{ activeClass }}"
                                    aria-expanded="false"
                                    aria-controls="dropdown-{{ entry.key }}"
                                    aria-label="dropdown-{{ entry.key }}"
                                >
                                    {{ entry.key }}
                                    <img
                                        class="cs-drop-icon"
                                        src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fdown.svg"
                                        alt="dropdown icon"
                                        width="15"
                                        height="15"
                                        decoding="async"
                                        aria-hidden="true"
                                    />
                                </button>

                                {# Dropdowns have another ul/li set up within the parent li, which gets rendered in the same way as a normal link #}
                                <ul
                                    class="cs-drop-ul"
                                    id="dropdown-{{ entry.key }}"
                                >
                                    {% for child in entry.children %}
                                        <li class="cs-drop-li">
                                            <a
                                                href="{{ child.url }}"
                                                class="cs-li-link cs-drop-link"
                                                >{{ child.key }}</a
                                            >
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                {# Normal pages are rendered as <a> tags, in the normal way you'd expect #}
                                <a
                                    href="{{ entry.url }}"
                                    class="cs-li-link {% if entry.url == page.url %}cs-active{% endif %}"
                                    {% if entry.url == page.url %}aria-current="page"{% endif %}
                                >
                                    {{ entry.key }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </nav>
        <a href="/contact/" class="cs-button-solid cs-nav-button">[Contact Button Text]</a>
        <button id="dark-mode-toggle" aria-label="dark mode toggle">
            <svg class="cs-moon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 480" style="enable-background:new 0 0 480 480" xml:space="preserve"><path d="M459.782 347.328c-4.288-5.28-11.488-7.232-17.824-4.96-17.76 6.368-37.024 9.632-57.312 9.632-97.056 0-176-78.976-176-176 0-58.4 28.832-112.768 77.12-145.472 5.472-3.712 8.096-10.4 6.624-16.832S285.638 2.4 279.078 1.44C271.59.352 264.134 0 256.646 0c-132.352 0-240 107.648-240 240s107.648 240 240 240c84 0 160.416-42.688 204.352-114.176 3.552-5.792 3.04-13.184-1.216-18.496z"/></svg>
            <img class="cs-sun" aria-hidden="true" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fsun.svg" decoding="async" alt="moon" width="15" height="15">
        </button>
    </div>
</header>
                                