---
title: "Services | [Your Brand Name]"
description: "[Meta description for your services page]"
preloadImg: "/assets/images/cabinets2.jpg"
permalink: "services/"
eleventyNavigation:
    key: Services
    order: 300
---

{% extends "layouts/base.html" %}

{% block head %}
    <link rel="stylesheet" href="/assets/css/services.css" />
{% endblock %}

{% block body %}
    <!-- ============================================ -->
    <!--                    LANDING                   -->
    <!-- ============================================ -->

    <section id="int-hero">
        <h1 id="home-h">Services</h1>
        <picture>
            <source
                media="(max-width: 600px)"
                srcset="/assets/images/banner-sm.webp"
            />
            <source
                media="(min-width: 601px)"
                srcset="/assets/images/banner.webp"
            />
            <img
                aria-hidden="true"
                decoding="async"
                src="/assets/images/banner.webp"
                alt="kitchen cabinets"
                loading="eager"
                width="2500"
                height="1667"
            />
        </picture>
    </section>

<!-- ============================================ -->
<!--                  Services                    -->
<!-- ============================================ -->

<section id="services-1082">
    <div class="cs-container">
        <div class="cs-content">
            <span class="cs-topper">Services</span>
            <h2 class="cs-title">[Your Services Section Heading]</h2>
            <p class="cs-text">
                [Overview of your services section. Describe what makes your service offerings unique and what customers can expect when working with you.]
            </p>
        </div>
        <ul class="cs-card-group">
            <li class="cs-item">
                <picture class="cs-picture">
                    <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/6474300/pexels-photo-6474300.jpeg" alt="service" width="413" height="240" aria-hidden="true">
                </picture>
                <h3 class="cs-h3">
                    <img class="cs-icon" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/engine.svg" alt="icon" width="32" height="32" aria-hidden="true">
                    [Service Name]
                </h3>
                <p class="cs-item-text">
                    [Brief description of this specific service and its benefits...]
                </p>
                <a href="" class="cs-link">
                    Read More
                    <img class="cs-arrow" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/red-arrow.svg" alt="icon" width="20" height="20" aria-hidden="true">
                </a>
            </li>
            <li class="cs-item">
                <picture class="cs-picture">
                    <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/5691495/pexels-photo-5691495.jpeg" alt="mechanic" width="413" height="240" aria-hidden="true">
                </picture>
                <h3 class="cs-h3">
                    <img class="cs-icon" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/steering-wheel.svg" alt="icon" width="32" height="32" aria-hidden="true">
                    Engine Overhaul
                </h3>
                <p class="cs-item-text">
                    If your engine isn't working properly chances are you're in for a costly repair. An engine replacement of expansion, succession...
                </p>
                <a href="" class="cs-link">
                    Read More
                    <img class="cs-arrow" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/red-arrow.svg" alt="icon" width="20" height="20" aria-hidden="true">
                </a>
            </li>
            <li class="cs-item">
                <picture class="cs-picture">
                    <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/6474133/pexels-photo-6474133.jpeg" alt="mechanic" width="413" height="240" aria-hidden="true">
                </picture>
                <h3 class="cs-h3">
                    <img class="cs-icon" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/oil-bottle.svg" alt="icon" width="32" height="32" aria-hidden="true">
                    Engine Overhaul
                </h3>
                <p class="cs-item-text">
                    If your engine isn't working properly chances are you're in for a costly repair. An engine replacement of expansion, succession...
                </p>
                <a href="" class="cs-link">
                    Read More
                    <img class="cs-arrow" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/red-arrow.svg" alt="icon" width="20" height="20" aria-hidden="true">
                </a>
            </li>
            <li class="cs-item">
                <picture class="cs-picture">
                    <img loading="lazy" decoding="async" src="https://images.pexels.com/photos/6473965/pexels-photo-6473965.jpeg" alt="mechanic" width="413" height="240" aria-hidden="true">
                </picture>
                <h3 class="cs-h3">
                    <img class="cs-icon" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/oil-bottle.svg" alt="icon" width="32" height="32" aria-hidden="true">
                    Engine Overhaul
                </h3>
                <p class="cs-item-text">
                    If your engine isn't working properly chances are you're in for a costly repair. An engine replacement of expansion, succession...
                </p>
                <a href="" class="cs-link">
                    Read More
                    <img class="cs-arrow" loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/red-arrow.svg" alt="icon" width="20" height="20" aria-hidden="true">
                </a>
            </li>
        </ul>
    </div>
</section>

<!-- ============================================ -->
<!--                    Steps                     -->
<!-- ============================================ -->

<section id="steps-1376">
    <div class="cs-container">
        <div class="cs-content">
            <span class="cs-topper">Our Process</span>
            <h2 class="cs-title">[Your Process Section Heading]</h2>
        </div>
        <!--
            We had to use a div here because of the grouping we had to do to make the staggerd design work. We needed to create two groups, one with 3 cards and one with 2 cards so we can arrange the cards separately to create the design we need to create. 
        -->
        <div class="cs-card-group">
            <div class="cs-group1">
                <div class="cs-item">
                    <picture class="cs-picture">
                        <img class="cs-icon" loading="lazy" decoding="async"
                            src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/step1.svg" alt="icon"
                            width="48" height="48" />
                    </picture>
                    <h3 class="cs-h3">[Step 1 Name]</h3>
                    <p class="cs-item-text">
                        [Brief description of the first step in your process]
                    </p>
                </div>
                <div class="cs-item">
                    <picture class="cs-picture">
                        <img class="cs-icon" loading="lazy" decoding="async"
                            src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/step2.svg" alt="icon" width="32"
                            height="32" />
                    </picture>
                    <h3 class="cs-h3">[Step 2 Name]</h3>
                    <p class="cs-item-text">
                        [Brief description of the second step in your process]
                    </p>
                </div>
                <div class="cs-item">
                    <picture class="cs-picture">
                        <img class="cs-icon" loading="lazy" decoding="async"
                            src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/step3.svg" alt="icon" width="32"
                            height="32" />
                    </picture>
                    <h3 class="cs-h3">[Step 3 Name]</h3>
                    <p class="cs-item-text">
                        [Brief description of the third step in your process]
                    </p>
                </div>
            </div>
            <div class="cs-group2">
                <div class="cs-item">
                    <picture class="cs-picture">
                        <img class="cs-icon" loading="lazy" decoding="async"
                            src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/step4.svg" alt="icon" width="32"
                            height="32" />
                    </picture>
                    <h3 class="cs-h3">Development</h3>
                    <p class="cs-item-text">
                        Lorem ipsum dolor sit amet consectetur, adipisicing elit. Praesentium, et?
                    </p>
                </div>
                <div class="cs-item">
                    <picture class="cs-picture">
                        <img class="cs-icon" loading="lazy" decoding="async"
                            src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons/step3.svg" alt="icon" width="32"
                            height="32" />
                    </picture>
                    <h3 class="cs-h3">Test & Launch</h3>
                    <p class="cs-item-text">
                        Lorem ipsum dolor sit amet consectetur, adipisicing elit. Praesentium, et?
                    </p>
                </div>
            </div>
            <picture class="cs-waves">
                <!--Mobile Image-->
                <source media="(max-width: 1023px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/wave-tablet.svg">
                <!--Tablet and above Image-->
                <source media="(min-width: 1024px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/wave-desktop.svg">
                <img loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images/Graphics/wave-desktop.svg" alt="wave" width="1280" height="568">
            </picture>
        </div>
    </div>
</section>

<!-- ============================================ -->
<!--                  Pricing                     -->
<!-- ============================================ -->

<section id="pricing-1319">
    <div class="cs-container">
        <div class="cs-content">
            <span class="cs-topper">Our Pricing</span>
            <h2 class="cs-title">[Your Pricing Section Heading]</h2>
            <p class="cs-text">
                [Brief description of your pricing structure and what makes it flexible/unique for customers]
            </p>
            <a href="" class="cs-button-solid">Call to Action</a>
        </div>
        <ul class="cs-card-group">
            <li class="cs-item">
                <span class="cs-package">[Package Name]</span>
                <span class="cs-desc">[Package Description]</span>
                <span class="cs-price">
                    [Price]
                    <span class="cs-duration">/[Duration]</span>
                </span>
                <ul class="cs-ul">
                    <li class="cs-li">[Feature 1]</li>
                    <li class="cs-li">[Feature 2]</li>
                    <li class="cs-li">[Feature 3]</li>
                </ul>
                <a href="" class="cs-button-transparent">Get Now</a>
            </li>
            <li class="cs-item cs-popular">
                <span class="cs-package">Corporate Pack</span>
                <span class="cs-desc">Designed for corporate clients</span>
                <span class="cs-price">
                    $599
                    <span class="cs-duration">/Month</span>
                </span>
                <ul class="cs-ul">
                    <li class="cs-li">0 Users</li>
                    <li class="cs-li">Unlimited Projects</li>
                    <li class="cs-li">24/7 Support</li>
                </ul>
                <a href="" class="cs-button-transparent">Get Now</a>
            </li>
            <li class="cs-item">
                <span class="cs-package">Standard Pack</span>
                <span class="cs-desc">Perfect for small business</span>
                <span class="cs-price">
                    $99
                    <span class="cs-duration">/Month</span>
                </span>
                <ul class="cs-ul">
                    <li class="cs-li">5 Users</li>
                    <li class="cs-li">20 Projects</li>
                    <li class="cs-li">Limited Support</li>
                </ul>
                <a href="" class="cs-button-transparent">Get Now</a>
            </li>
        </ul>
    </div>
</section>

{% endblock %}